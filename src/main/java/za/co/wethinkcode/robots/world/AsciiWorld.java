package za.co.wethinkcode.robots.world;

import za.co.wethinkcode.robots.Position;
import za.co.wethinkcode.robots.obstacle.Obstacle;
import za.co.wethinkcode.robots.robot.Robot;
import java.io.FileWriter;
import java.io.IOException;
import static za.co.wethinkcode.robots.config.Config.*;

/**
 * The AsciiWorld class is responsible for generating an ASCII representation of the world.
 * It takes a World object and creates a text file with the current state of the world,
 * including obstacles and robots.
 */
public class AsciiWorld {
    private StringBuilder content = new StringBuilder();
    private World world ;
    int width = WIDTH;  // Use WIDTH from Config
    int height = HEIGHT;


    /**
     * Constructor for AsciiWorld.
     * @param world The World object representing the current state of the world.
     */
    public AsciiWorld(World world) {
        this.world =world;
        content.append("\n------Legend-------\n" +
                "MOUNTAIN :      '^'\n" +
                "LAKE :          '~'\n" +
                "BOTTOMLESS_PIT :'■'\n");
    }

    /**
     * Returns the content of the ASCII representation.
     * @return A StringBuilder containing the ASCII representation of the world.
     */
    public StringBuilder getContent() {
        return content;
    }

    /**
     * Generates an ASCII representation of the world and writes it to a text file.
     */
    public String[][] printToTxt() {
        String[][] grid = initializeGrid();
        fillObstaclesInGrid(grid);
        fillRobotsInGrid(grid);
        return grid;
    }

    private String[][] initializeGrid() {
        String[][] grid = new String[height][width];
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                grid[y][x] = " ";
            }
        }
        return grid;
    }

    private void fillObstaclesInGrid(String[][] grid) {
        for (Obstacle obstacle : world.getObstacles()) {
            Position topLeft = obstacle.getTopLeft();
            Position bottomRight = obstacle.getBottomRight();
            String chr = switch (obstacle.getType()) {
                case MOUNTAIN -> "^";
                case LAKE -> "~";
                case BOTTOMLESS_PIT -> "■";
            };
            fillObstacleAreaInGrid(grid, topLeft, bottomRight, chr);
        }
    }

    private void fillObstacleAreaInGrid(String[][] grid, Position topLeft, Position bottomRight, String chr) {
        for (int y = topLeft.getY(); y <= bottomRight.getY(); y++) {
            for (int x = topLeft.getX(); x <= bottomRight.getX(); x++) {
                if (isInsideGrid(x, y, width, height)) {
                    grid[y][x] = chr;
                }
            }
        }
    }

    private void fillRobotsInGrid(String[][] grid) {
        for (Robot robot : world.getBots()) {
            Position pos = robot.getPosition();
            char firstLetter = Character.toUpperCase(robot.getName().charAt(0));
            grid[pos.getY()][pos.getX()] = String.valueOf(firstLetter);
        }
    }

    public void buildAscii(){
        String[][] grid = printToTxt();

        content.append(" ");
        content.append("_".repeat(width));
        content.append("\n");
        for (int y = 0; y < height; y++) {
            content.append("|");
            for (int x = 0; x < width; x++) {
                content.append(grid[y][x]);
            }
            content.append("|\n");
        }

        content.append(" ");
        content.append("-".repeat(width));
        content.append("\n");


        try (FileWriter writer = new FileWriter("Ascii-World.txt")) {
            writer.write(content.toString());
            System.out.println("\nSuccessfully wrote world to Ascii-World.txt");
        } catch (IOException e) {
            System.out.println("\nFailed to write to file: " + e.getMessage());
        }
    }

    /**
     * Checks if the given coordinates are inside the grid boundaries.
     * @param x The x-coordinate.
     * @param y The y-coordinate.
     * @param width The width of the grid.
     * @param height The height of the grid.
     * @return true if the coordinates are inside the grid, false otherwise.
     */
    private boolean isInsideGrid(int x, int y, int width, int height) {
        return x >= 0 && x < width && y >= 0 && y < height;
    }
}
