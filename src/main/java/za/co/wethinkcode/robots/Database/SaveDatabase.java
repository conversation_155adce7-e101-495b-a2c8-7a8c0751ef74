package za.co.wethinkcode.robots.Database;



import za.co.wethinkcode.robots.command.SaveCommand;
import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.obstacle.Obstacle;
import za.co.wethinkcode.robots.obstacle.ObstacleType;
import za.co.wethinkcode.robots.server.MultiServers;
import za.co.wethinkcode.robots.server.WorldInstanceHelper;
import za.co.wethinkcode.robots.world.World;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * RecipeDbDemo shows how to read/write/update/delete data using the example Brewery Database.
 */
public class SaveDatabase {
    public static final String DISK_DB_URL_PREFIX = "jdbc:sqlite:world.db";
    public MultiServers server;
    public SaveCommand save;
//    public static final String SEPARATOR = "\t";

//    public static void main( String[] args ) {
//        final SaveDatabase app = new SaveDatabase( args );
//    }

    private String dbUrl;
//    public String getDbUrl(){return dbUrl;}

    public SaveDatabase() throws SQLException {
        this.dbUrl = DISK_DB_URL_PREFIX;
//        this.save = save;
//        this.server = server;
    }

    //        processCmdLineArgs( args );
    public void createConnection() {
        try (Connection connection = DriverManager.getConnection(dbUrl)) { // <1>Other ways to get a Connection
            useTheDb(connection);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    public Connection getConnection() throws SQLException {
        return DriverManager.getConnection(dbUrl);
    }


    public void useTheDb(final Connection connection) throws SQLException {
        int worldID = createData(connection);
        World world = MultiServers.getServer().getWorld();
        obstacleData(connection, world, worldID);
        restoreWorld(connection, save.getWorldName());
    }

    public void saveTheWorldByName(final Connection connection) throws SQLException {
        int worldID = createData(connection);
        World world = MultiServers.getServer().getWorld();
        obstacleData(connection, world, worldID);
    }

    private int createData(final Connection connection)
            throws SQLException {

        try (final PreparedStatement statement = connection.prepareStatement("INSERT INTO world ( height,width,name) VALUES (?,?,?)")) {
            statement.setInt(1, this.server.getHeight());
            statement.setInt(2,this.server.getWidth());
            statement.setString(3, save.getWorldName());
            statement.executeUpdate();

            try (ResultSet keys = statement.getGeneratedKeys()) {
                if (keys.next()) {
                    return keys.getInt(1);
                } else {
                    throw new SQLException("No world ID returned.");
                }
            }
        }
    }

    public int getData() {
        try (Connection connection = getConnection()) {
            return createData(connection);
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return 0;
    }

    private void obstacleData(final Connection connection, World world, int worldID) throws SQLException {
        String obstacleString = this.server.getObstacle(); // e.g. "1,2;3,4"

        if (obstacleString.equalsIgnoreCase("none")) {
            System.out.println("No obstacles to save.");
            return;
        }

        String[] obstacleCoords = obstacleString.split(";");

        try (final PreparedStatement statement = connection.prepareStatement(
                "INSERT INTO obstacles (position, worldID) VALUES (?, ?)")) {

            for (String coord : obstacleCoords) {
                String trimmedCoord = coord.trim();
                if (!trimmedCoord.isEmpty()) {
                    statement.setString(1, trimmedCoord); // e.g., "1,2"
                    statement.setInt(2, worldID);
                    statement.executeUpdate();
                }
            }
        }
    }

    public World restoreWorld(final Connection conn, String name) throws SQLException {

        try {
            // Get world size
            PreparedStatement worldStmt = conn.prepareStatement("SELECT height ,width FROM world WHERE name=?");
            worldStmt.setString(1, name);
            ResultSet result = worldStmt.executeQuery();

            if (!result.next()) {
                System.out.println("World not found.");
                return null;
            }

            int height= result.getInt("height");
            int width= result.getInt("width");
            System.out.println("New world size should be: "+height + ","+ width);
            result.close();
            worldStmt.close();

            // Prepare maze
            Maze maze = new Maze(" "); // avoid random generation
            ObstacleType type = ObstacleType.MOUNTAIN;

            // Get obstacles
            PreparedStatement stment = conn.prepareStatement("SELECT position FROM obstacles WHERE name=?");
            stment.setString(1, name);
            ResultSet output = stment.executeQuery();

            while (output.next()) {
                String position = output.getString("position");  // e.g. "1,2;3,4"
                String[] parts = position.split(";");
                if (parts.length != 2) continue;

                String[] firstCoords = parts[0].split(",");
                String[] secondCoords = parts[1].split(",");

                int firstX = Integer.parseInt(firstCoords[0].trim());
                int firstY = Integer.parseInt(firstCoords[1].trim());
                int secX = Integer.parseInt(secondCoords[0].trim());
                int secY = Integer.parseInt(secondCoords[1].trim());

                maze.getObstacleList().add(new Obstacle(firstX, firstY, secX, secY, type));
            }

            output.close();
            stment.close();

            // Now create the World once
            boolean enableGUI = !java.awt.GraphicsEnvironment.isHeadless();
            World restoredWorld = new World(enableGUI, maze.toString());
            MultiServers.getWorldInstance().setWorldwidth(width);
            MultiServers.getWorldInstance().setWorldheight(height);


            World world = MultiServers.getWorldInstance();
            if (world != null && world.getGui() != null) {
                world.getGui().close();
            }

            MultiServers.setWorldInstance(restoredWorld);
//            System.out.println("The old world size was: "+MultiServers.getWorldInstance().getWorldheight()+","+MultiServers.getWorldInstance().getWorldwidth());

            System.out.println("The new world size is: "+MultiServers.getWorldInstance().getWorldheight()+" ," +MultiServers.getWorldInstance().getWorldwidth());
            return restoredWorld;
        } catch (SQLException e) {
            System.err.println("Error restoring world: " + e.getMessage());
            return null;
        }
    }

    public boolean ifNameExists(final Connection connection, String worldName) throws SQLException {
        try (PreparedStatement state = connection.prepareStatement("SELECT COUNT(*) FROM world WHERE name=?  ")) {
            state.setString(1, worldName);
            ResultSet rs = state.executeQuery();
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            System.out.println("Error checking world existence: " + e.getMessage());
        }
        return false;
    }

    public void deleteWorldByName(String worldName) {
        String sql = "DELETE FROM world WHERE name = ?";
        try (Connection conn = this.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, worldName);
            pstmt.executeUpdate();
        } catch (SQLException e) {
            System.out.println("Error deleting existing world: " + e.getMessage());
        }
    }
}



