package za.co.wethinkcode.robots.server;

import java.io.IOException;

public class ServerStartupHelper {
    
    public static boolean startServer(MultiServerEngine server, int port) {
        try {
            server.start(port);
            return true;
        } catch (IOException e) {
            System.err.println("Failed to bind to port: " + e.getMessage());
            return false;
        }
    }
}
