package za.co.wethinkcode.robots.server;

import za.co.wethinkcode.robots.config.Config;

public class ConfigOverrideHelper {

    public static void OverRideConfigProperties(MultiServers command) {
        Config.PORT = command.getPort();

        if (command.getHeight() > 0 && command.getWidth() > 0) {
            Config.HEIGHT = command.getHeight();
            Config.WIDTH = command.getWidth();
            Config.OBSTACLE_MODE = "none";
        }

        if (!command.getObstacle().equalsIgnoreCase("none")) {
            Config.OBSTACLE_MODE = "none";
        }
    }
}
