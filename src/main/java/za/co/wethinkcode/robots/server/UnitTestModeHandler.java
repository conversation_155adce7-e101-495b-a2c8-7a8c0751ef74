package za.co.wethinkcode.robots.server;

import java.io.IOException;

public class UnitTestModeHandler {
    
    public static void handleUnitTestMode(MultiServerEngine server, Runnable setUnitTestMode) {
        setUnitTestMode.run();
        Thread timeoutThread = new Thread(() -> {
            try {
                Thread.sleep(60000); // Increased timeout to 60 seconds for tests
                System.out.println("Timeout thread interrupted, normal shutdown");
            } catch (InterruptedException e) {
                System.out.println("Timeout thread interrupted, normal shutdown");
            }
        });
        timeoutThread.setDaemon(true);
        timeoutThread.start();

        try {
            while (!Thread.currentThread().isInterrupted() && !server.isClosed()) {
                Thread.sleep(100); // Slightly longer sleep to reduce CPU usage
            }
        } catch (InterruptedException e) {
            timeoutThread.interrupt();
        }

        try {
            if (!server.isClosed()) {
                server.shutdown();
            }
        } catch (IOException e) {
            System.err.println("Error during shutdown: " + e.getMessage());
        }
        timeoutThread.interrupt();
    }
}
