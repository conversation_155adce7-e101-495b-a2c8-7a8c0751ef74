package za.co.wethinkcode.robots.server;

import za.co.wethinkcode.robots.command.DumpCommand;
import za.co.wethinkcode.robots.command.RestoreCommand;
import za.co.wethinkcode.robots.command.RobotsCommand;
import za.co.wethinkcode.robots.command.SaveCommand;
import za.co.wethinkcode.robots.world.World;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Scanner;

public class ServerCommandHandler {
    
    public static void handleCommands(Scanner scanner, World worldInstance, MultiServerEngine server,MultiServers servers) throws SQLException {
        while (true) {
            System.out.print("Server Command> ");

//            String command;
            String input;
//            String[] parts = input.split("\\s+");
            try {
                input = scanner.nextLine().toLowerCase().trim();
            } catch (Exception e) {
                System.out.println("Input error, shutting down server: " + e.getMessage());
                break;
            }
            String[] parts = input.split("\\s+");
            String command = parts[0].toLowerCase();
            switch (command) {
                case "quit":
                case "shutdown":
                    System.out.println("Shutting down server...");
                    server.broadcastMessage("quit");
                    try {
                        server.shutdown();
                    } catch (IOException e) {
                        System.out.println("Error shutting down: " + e.getMessage());
                    }
                    return;
                case "dump":
                    DumpCommand.getInstance().dump(worldInstance);
                    break;
                case "robots":
                    RobotsCommand.getInstance().printRobots(worldInstance);
                    break;
                case "save":
                    SaveCommand.getInstance().handleInput(input,servers);
                    break;
                case "restore":
                    RestoreCommand.getInstance().handleInput(input);
                    break;

                default:
                    System.out.println("Unknown command: " + command);
                    break;
            }
        }
    }
}
