package za.co.wethinkcode.robots.command;

import com.google.gson.JsonObject;
import za.co.wethinkcode.robots.Database.SaveDatabase;
import za.co.wethinkcode.robots.server.MultiServers;
import za.co.wethinkcode.robots.world.World;

import java.sql.SQLException;
import java.util.Scanner;


public class SaveCommand extends Command{
    private static SaveCommand instance;
    private String name;
    private MultiServers server;

    public SaveCommand(){
        super("save");
    }
    public static synchronized SaveCommand getInstance() {
        if (instance == null) {
            instance = new SaveCommand();
        }
        return instance;
    }
    public void setWorldname(String worldname) {
        this.name = worldname;
    }

    public String getWorldName(){
        return this.name;
    }
    @Override
    public JsonObject execute(World world) {
        return null;
    }

    public void handleInput(String input,MultiServers server) {

        String[] parts = input.trim().split("\\s+");
        if (parts.length < 2) {
            System.out.println("Please specify the world name to save");
            return;
        }

        try {
            String name = parts[1];
            setWorldname(name);
            this.server=server;
            this.save();  // Call restore logic
        } catch (NumberFormatException | SQLException e) {
            System.out.println("Invalid world name format.");
        }
    }



    public void save() throws SQLException {
        System.out.println("Saving world...");
        try {
            SaveDatabase saveDatabase = new SaveDatabase();
            saveDatabase.server = this.server;
            saveDatabase.save = this;
            if(saveDatabase.ifNameExists(saveDatabase.getConnection(), getWorldName())){
                System.out.println("This world name " +getWorldName()+" already exists in our database.");
                System.out.println("Would you like to overwrite the existing one?");
                Scanner scanner = new Scanner(System.in);
                String response = scanner.nextLine().trim().toLowerCase();
                if (response.equalsIgnoreCase("yes")){
                    saveDatabase.deleteWorldByName(getWorldName());
                }else{
                    System.out.println("Abhorting...");
                    return;
                }

            }
            saveDatabase.saveTheWorldByName(saveDatabase.getConnection());
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }


}

