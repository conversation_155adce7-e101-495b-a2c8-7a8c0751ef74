package za.co.wethinkcode.robots.command;

import com.google.gson.JsonObject;
import za.co.wethinkcode.robots.Position;
import za.co.wethinkcode.robots.obstacle.Obstacle;
import za.co.wethinkcode.robots.obstacle.ObstacleType;
import za.co.wethinkcode.robots.robot.Robot;
import za.co.wethinkcode.robots.world.World;

public class FireCommand extends Command {

    private static FireCommand instance;

    private FireCommand() {
        super("fire");
    }

    public static synchronized FireCommand getInstance() {
        if (instance == null) instance = new FireCommand();
        return instance;
    }

    @Override
    public JsonObject execute(World world) {
        JsonObject response = new JsonObject();
        JsonObject data = new JsonObject();

        Robot currentRobot = world.getCurrentRobot();
        if (currentRobot == null) {
            response.addProperty("result", "ERROR");
            data.addProperty("message", "Cannot perform 'fire': No robot context active.");
            response.add("data", data);
            return response;
        }

        response.addProperty("result", "OK");
        String message = "Miss";
        boolean hitObstacle = checkIfHitObstacle(world, currentRobot);
        if (!hitObstacle) {
            message = processRobotHits(world, currentRobot, data);
        }

        currentRobot.decrementShot();
        data.addProperty("message", message);
        response.add("data", data);
        return response;
    }

    private String processRobotHits(World world, Robot currentRobot, JsonObject data) {
        String message = "Miss";
        for (Robot robot : world.getBots()) {
            if (currentRobot.equals(robot)) continue;
            message = processRobotHit(currentRobot, robot, data, message);
            if ("Hit".equals(message)) {
                break;
            }
        }
        return message;
    }

    private String processRobotHit(Robot currentRobot, Robot robot, JsonObject data, String message) {
        if (currentRobot.getShots() > 0) {
            if (currentRobot.hit(robot)) {
                message = "Hit";
                populateHitData(currentRobot, robot, data);
            }
        } else {
            message = "Out of ammo.";
        }
        return message;
    }

    private void populateHitData(Robot currentRobot, Robot robot, JsonObject data) {
        data.addProperty("distance", currentRobot.getPosition()
                .distanceFrom(robot.getPosition(), currentRobot.getCurrentDirection()));
        data.addProperty("robot", robot.getName());
        data.add("state", robot.state());
    }

    private boolean checkIfHitObstacle(World world, Robot currentRobot) {
        for (Obstacle obstacle : world.getObstacles()) {
            if (obstacle.getType() == ObstacleType.MOUNTAIN) {
                if (checkObstaclesInDirection(world, currentRobot)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean checkObstaclesInDirection(World world, Robot currentRobot) {
        int x = 1, y = 1;
        for (int i = 1; i < currentRobot.getBulletDistance(); i++) {
            switch (currentRobot.getCurrentDirection()) {
                case NORTH -> y = -1;
                case WEST -> x = -1;
            }
            if (world.isMovementObstructed(new Position(
                    x * i + currentRobot.getPosition().getX(),
                    y * i + currentRobot.getPosition().getY()
            ))) {
                return true;
            }
        }
        return false;
    }
}
