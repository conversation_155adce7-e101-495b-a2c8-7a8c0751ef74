package za.co.wethinkcode.robots.command;

import com.google.gson.JsonObject;
import za.co.wethinkcode.robots.Database.SaveDatabase;
import za.co.wethinkcode.robots.server.MultiServers;
import za.co.wethinkcode.robots.world.World;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class RestoreCommand extends Command{

    private static RestoreCommand instance;
    private String worldName;
    public RestoreCommand(){
        super("restore");
    }
    public static synchronized RestoreCommand getInstance() {
        if (instance == null) {
            instance = new RestoreCommand();
        }
        return instance;
    }
    public void setWorldName(String name) {
        this.worldName= name;
    }
    @Override
    public JsonObject execute(World world) {
        return null;
    }

    public void handleInput(String input) {
        String[] parts = input.trim().split("\\s+");
        if (parts.length < 2) {
            System.out.println("Please specify the world ID to restore.");
            return;
        }

        try {
            String name= parts[1];
            setWorldName(name);
            this.restore();  // Call restore logic
        } catch (NumberFormatException | SQLException e) {
            System.out.println("Invalid world ID format.");
        }
    }
    public void restore() throws SQLException {
        // write code that will take go into the database retrieve the name of the world and start the server
        System.out.println("Restoring ....");
//        if (worldId <= 0) {
//            System.out.println("Invalid or missing world ID.");
//            return;
//        }
        try (Connection conn = DriverManager.getConnection("jdbc:sqlite:world.db")) {
            SaveDatabase saveDatabase = new SaveDatabase();
            World restoredWorld = saveDatabase.restoreWorld(conn, worldName);

            if (restoredWorld != null) {
                MultiServers.getServer().setWorld(restoredWorld);
                System.out.println("World restored successfully.");
                // Build and return success JsonObject if needed
            } else {
                System.out.println("World not found.");
                // Build and return failure JsonObject if needed
            }
        } catch (SQLException e) {
            e.printStackTrace();
            // Build and return error JsonObject if needed
        }
        return;
    }
}



