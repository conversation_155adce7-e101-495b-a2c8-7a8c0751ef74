package za.co.wethinkcode.robots.client;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

public class ResponseFormatter {
    
    public static String formatResponse(JsonObject response) {
        String output = "< Server Response:\n";
        String result = response.has("result") ? response.get("result").getAsString() : "unknown";
        output += "  Result: " + result + "\n";

        if (response.has("data")) {
            output += showData(response.getAsJsonObject("data"));
        }

        if (response.has("state")) {
            output += showState(response.getAsJsonObject("state"));
            
            String stateStr = response.get("state").toString();
            if (stateStr.contains("REPAIR")) {
                output += "\n ROBOT IS IN REPAIR MODE: COMMANDS CAN NOT BE TYPED OR PROCESSED\n";
                return output;
            } else if (stateStr.contains("RELOAD")) {
                output += "\n ROBOT IS IN RELOAD MODE: COMMANDS CAN NOT BE TYPED OR PROCESSED\n";
                return output;
            }
        }

        return output;
    }
    
    public static String formatResponseWithPrompt(JsonObject response, String robotName) {
        return formatResponse(response) + robotName + " > Enter command: ";
    }
    
    public static String formatServerResponse(JsonObject response) {
        String output = "\n< Client Response:\n";
        String result = response.has("result") ? response.get("result").getAsString() : "unknown";
        output += "  Result: " + result + "\n";

        if (response.has("data")) {
            output += showData(response.getAsJsonObject("data"));
        }

        if (response.has("state")) {
            output += showState(response.getAsJsonObject("state"));
        }
        output += "Server Command> ";

        return output;
    }

    private static String showData(JsonObject data) {
        String output = "";

        if (data.has("message")) {
            output += "  Message: " + data.get("message").getAsString() + "\n";
            if (data.get("message").getAsString().contains("Hit")) {
                output += showHitState(data);
            }
        }

        if (data.has("orientation")) {
            output += "  Orientation: " + data.get("orientation").getAsString() + "\n";
        }

        if (data.has("objects")) {
            output += showObjects(data.getAsJsonArray("objects"));
        }
        
        return output;
    }
    
    private static String showObjects(JsonArray objects) {
        String output = "  Objects Seen:\n";
        
        if (objects.size() == 0) {
            output += "    - Nothing detected.\n";
        } else {
            for (JsonElement el : objects) {
                JsonObject obj = el.getAsJsonObject();
                output += showSingleObject(obj);
            }
        }
        
        return output;
    }
    
    private static String showSingleObject(JsonObject obj){
        String type = obj.has("type") ? obj.get("type").getAsString() : "unknown";
        String dir = obj.has("direction") ? obj.get("direction").getAsString() : "unknown";
        int dist = obj.has("distance") ? obj.get("distance").getAsInt() : -1;
        
        String description = type;
        if (type.equals("OBSTACLE") && obj.has("obstacle_type")){
            description += " (" + obj.get("obstacle_type").getAsString() + ")";
        } else if (type.equals("ROBOT") && obj.has("name")) {
            description += " (" + obj.get("name").getAsString() + ")";
        }

        String result = "    - " + description + " [Direction: " + dir + ", Distance: ";
        if (dist == 0) {
            result += dir + " YOU ARE ON THE EDGE BE CAREFUL!";
        } else {
            result += dist;
        }
        result += "]\n";
        return result;
    }
    
    private static String showHitState(JsonObject data) {
        if (data.has("state") && data.has("robot")){
            return showState(data.getAsJsonObject("state"), data.get("robot").getAsString());
        }
        return "";
    }
    
    public static String showState(JsonObject state) {
        return showState(state, null);
    }
    
    public static String showState(JsonObject state, String robotName) {
        String output = "";
        
        if (robotName != null) {
            output += "  State of robot " + robotName + ":\n";
        } else {
            output += "  State:\n";
            if (state.has("name")) {
                output += "    - Name: " + state.get("name").getAsString() + "\n";
            }
        }
        
        output += "    - Position: " + getValue(state, "position") + "\n";
        output += "    - Make: " + getValue(state, "make") + "\n";
        output += "    - Direction: " + getValue(state, "direction") + "\n";
        output += "    - Shields: " + getValue(state, "shields") + "\n";
        output += "    - Shots: " + getValue(state, "shots") + "\n";
        
        if (state.has("status") && !state.get("status").getAsString().equals("DEAD")) {
            output += "    - Status: " + getValue(state, "status") + "\n";
        }

        return output;
    }
    
    private static String getValue(JsonObject obj, String field) {
        if (!obj.has(field)) {
            return "unknown";
        }
        
        JsonElement element = obj.get(field);
        if (element.isJsonArray()) {
            return element.toString();
        } else {
            return element.getAsString();
        }
    }
}
