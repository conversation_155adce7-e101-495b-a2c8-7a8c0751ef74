package za.co.wethinkcode.robots.client;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintStream;
import java.util.Scanner;

public class LaunchHandler {
    
    public static String handleLaunchProcess(Scanner scanner, PrintStream out, BufferedReader in) {
        while (true) {
            String line = promptForCommand(scanner);

            if (!isLaunchCommand(line)) {
                System.out.println("Invalid command. Format: launch <robottype> <robotname> or launch <robottype> <robotname> <shields> <shots>");
                continue;
            }

            String[] parts = line.split(" ");
            String type = parts[1].toLowerCase();
            String name = parts[2];

            if (!isGoodRobotType(type)) {
                System.out.println("Invalid robot type. Valid types: sniper, soldier, hitbot");
                continue;
            }

            JsonObject request = buildLaunchRequest(parts, name, type);
            if (request == null) {
                continue;
            }

            if (sendAndHandleRequest(out, in, request, name)) {
                return name;
            }
        }
    }

    private static String promptForCommand(Scanner scanner){
        System.out.print("> Enter command: ");
        return scanner.nextLine().trim();
    }

    private static JsonObject buildLaunchRequest(String[] parts, String name, String type) {
        if (parts.length == 3){
            return makeLaunchRequest(name, type);
        }
        try {
            int shields = Integer.parseInt(parts[3]);
            int shots = Integer.parseInt(parts[4]);
            if (shields <= 0 || shots <= 0) {
                System.out.println("Shields and shots must be positive numbers");
                return null;
            }
            return makeLaunchRequest(name, type, shields, shots);
        }catch (NumberFormatException e){
            System.out.println("Invalid numbers for shields and shots");
            return null;
        }
    }

    private static boolean sendAndHandleRequest(PrintStream out, BufferedReader in, JsonObject request, String name) {
        try {
            out.println(request);
            out.flush();

            String response = in.readLine();
            if (response == null) {
                System.out.println("No response from server. Retrying...");
                return false;
            }

            JsonObject responseJson = JsonParser.parseString(response).getAsJsonObject();
            String result = responseJson.has("result") ? responseJson.get("result").getAsString() : "";

            if ("OK".equalsIgnoreCase(result)) {
                System.out.println("Robot launched successfully.");
                String formatted = ResponseFormatter.formatResponse(responseJson);
                System.out.print(formatted);
                return true;
            } else {
                String error = getErrorMessage(responseJson);
                System.out.println("Launch error: " + error);
                String formatted = ResponseFormatter.formatResponse(responseJson);
                System.out.print(formatted);
                return false;
            }

        } catch (IOException e) {
            System.out.println("Failed to get response. Retrying... " + e.getMessage());
        } catch (Exception e) {
            System.out.println("Something went wrong. Retrying... " + e.getMessage());
        }
        return false;
    }

    private static boolean isLaunchCommand(String line) {
        String[] parts = line.split(" ");
        return (parts.length == 3 || parts.length == 5) && parts[0].equalsIgnoreCase("launch");
    }
    
    private static boolean isGoodRobotType(String type) {
        return type.equals("sniper") || type.equals("soldier") || type.equals("hitbot");
    }
    
    private static JsonObject makeLaunchRequest(String name, String type, Integer shields, Integer shots) {
        JsonObject request = new JsonObject();
        request.addProperty("robot", name);
        request.addProperty("command", "launch");

        JsonArray args = new JsonArray();
        args.add(type);

        if (shields != null && shots != null) {
            args.add(shields);
            args.add(shots);
        }

        request.add("arguments", args);

        return request;
    }

    private static JsonObject makeLaunchRequest(String name, String type) {
        return makeLaunchRequest(name, type, null, null);
    }
    
    private static String getErrorMessage(JsonObject response) {
        if (response.has("data")) {
            JsonObject data = response.getAsJsonObject("data");
            return data.has("message") ? data.get("message").getAsString() : "Unknown error";
        }
        return "Unknown error";
    }
}
