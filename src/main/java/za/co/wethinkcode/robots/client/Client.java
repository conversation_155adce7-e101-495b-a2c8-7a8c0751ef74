package za.co.wethinkcode.robots.client;

import com.google.gson.*;
import java.io.*;
import java.net.*;
import java.util.Scanner;
import static za.co.wethinkcode.robots.config.Config.*;

public class Client {

    private static String myRobotName;

    public static void main(String[] args) {
        loadConfig("config.properties");

        System.out.println("Starting robot client...");

        try {
            Socket socket = new Socket(HOST, PORT);
            PrintStream out = new PrintStream(socket.getOutputStream());
            BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
            Scanner input = new Scanner(System.in);

            System.out.println("Connected! Ready to launch robot.");
            System.out.println("Usage: launch <type> <name>");
            System.out.println("Usage: launch <type> <name>");
            System.out.println("Available types: sniper, soldier, hitbot");

            myRobotName = LaunchHandler.handleLaunchProcess(input, out, in);

            Thread listener = new Thread(() -> ServerCommunicator.listenToServer(in, myRobotName));
            listener.setDaemon(true);
            listener.start();

            while (true) {
                String cmd = input.nextLine().trim();
                if (cmd.isEmpty()) {
                    continue;
                }

                try {
                    JsonObject request = CommandParser.parseCommand(cmd, myRobotName);
                    out.println(request);
                    out.flush();
                } catch (Exception e) {
                    System.out.println("Bad command: " + e.getMessage());
                    System.out.print(myRobotName + " > ");
                }
            }

        } catch (IOException e) {
            System.out.println("Connection failed: " + e.getMessage());
            System.out.println("Is the server running?");
        }
    }

    public static String formatServerResponse(JsonObject responseJson) {
        return ResponseFormatter.formatServerResponse(responseJson);
    }

    public static String formatState(JsonObject state) {
        return ResponseFormatter.showState(state);
    }

    public static String formatState(JsonObject state, String name) {
        return ResponseFormatter.showState(state, name);
    }

    public static JsonObject buildJsonCommand(String command, String robotName) {
        return CommandParser.parseCommand(command, robotName);
    }
}
