package za.co.wethinkcode.robots.client;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.io.BufferedReader;
import java.io.IOException;

public class ServerCommunicator {
    
    public static void listenToServer(BufferedReader in, String robotName) {
        try {
            String response;
            while ((response = in.readLine()) != null) {
                if (response.equalsIgnoreCase("quit")) {
                    System.out.println("\nServer wants to quit. Bye!");
                    System.exit(0);
                }

                try {
                    JsonObject responseJson = JsonParser.parseString(response).getAsJsonObject();
                    String formatted = ResponseFormatter.formatResponseWithPrompt(responseJson, robotName);
                    System.out.print("\n" + formatted);

                    if (robotIsDead(formatted)) {
                        handleDeath();
                    }

                } catch (Exception e) {
                    System.out.println("\nBad response from server:");
                    System.out.println("  Raw: " + response + " Error: " + e.getMessage());
                }
            }
        } catch (IOException e) {
            System.out.println("Lost connection to server. " + e.getMessage());
        }
    }
    
    private static boolean robotIsDead(String formatted) {
        return formatted.contains("DEAD") || formatted.contains("QUIT");
    }
    
    private static void handleDeath() {
        System.out.println("Robot is dead!");
        System.out.println("Your robot got destroyed.");
        System.out.println("\nRobot dead, bye!");
        System.exit(0);
    }
}
