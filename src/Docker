# Robot Worlds Server - Docker Guide

This guide explains how to build, run, and test the Robot Worlds server using Docker.

## Prerequisites

- Docker installed on your machine
- Git access to the project repository
- Access to GitLab Container Registry (for publishing)

## Quick Start

### 1. Build the Docker Image

First, build the Java application:
```bash
# Windows
build.bat build

# Unix/Linux/Mac
make build
```

Then build the Docker image:
```bash
# Windows
build.bat docker-build

# Unix/Linux/Mac
make docker-build
```

### 2. Run the Docker Container

```bash
# Windows
build.bat docker-run

# Unix/Linux/Mac
make docker-run
```

The server will be available at `http://localhost:5050`

### 3. Stop the Container

```bash
# Windows
build.bat docker-stop

# Unix/Linux/Mac
make docker-stop
```

## Manual Docker Commands

### Build Image
```bash
docker build -t robot-worlds-server:2.0.0 .
```

### Run Container
```bash
# Run in background
docker run -d --name robot-worlds-server -p 5050:5050 robot-worlds-server:2.0.0

# Run interactively (see logs)
docker run --name robot-worlds-server -p 5050:5050 robot-worlds-server:2.0.0
```

### View Logs
```bash
docker logs robot-worlds-server
```

### Stop and Remove Container
```bash
docker stop robot-worlds-server
docker rm robot-worlds-server
```

## Testing with Docker

### Run Acceptance Tests Against Docker Container
```bash
# Windows
build.bat docker-test

# Unix/Linux/Mac
make docker-test
```

This will:
1. Build the Docker image
2. Start a test container on port 5051
3. Run unit tests against the container
4. Clean up the test container

## GitLab Container Registry

### Pulling from Registry

Once the image is published to GitLab Container Registry, team members can pull and run it:

```bash
# Login to GitLab Container Registry
docker login registry.gitlab.wethinkco.de

# Pull the latest image
docker pull registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:latest

# Run the pulled image
docker run -d --name robot-worlds-server -p 5050:5050 registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:latest
```

### Publishing to Registry

Publishing happens automatically via GitLab CI/CD when code is pushed to main branch.

Manual publishing:
```bash
# Windows
build.bat docker-publish

# Unix/Linux/Mac
make docker-publish
```

## Configuration

### Port Configuration

The Docker container runs on port 5050 by default. You can map it to different host ports:

```bash
# Run on host port 8080
docker run -d --name robot-worlds-server -p 8080:5050 robot-worlds-server:2.0.0

# Run on host port 3000
docker run -d --name robot-worlds-server -p 3000:5050 robot-worlds-server:2.0.0
```

### Custom Server Arguments

You can pass custom arguments to the server:

```bash
# Custom world size
docker run -d --name robot-worlds-server -p 5050:5050 robot-worlds-server:2.0.0 java -jar app.jar -p 5050 -s 5

# With obstacles
docker run -d --name robot-worlds-server -p 5050:5050 robot-worlds-server:2.0.0 java -jar app.jar -p 5050 -s 3 -o 1,1
```

## Troubleshooting

### Container Won't Start
```bash
# Check container logs
docker logs robot-worlds-server

# Check if port is already in use
netstat -an | grep 5050  # Unix/Linux/Mac
netstat -an | findstr 5050  # Windows
```

### Build Issues
```bash
# Clean up Docker cache
docker system prune

# Rebuild without cache
docker build --no-cache -t robot-worlds-server:2.0.0 .
```

### Registry Access Issues
```bash
# Login to GitLab Container Registry
docker login registry.gitlab.wethinkco.de
# Enter your GitLab username and access token
```

## Team Member Setup

Each team member should be able to run the Docker image. Here's the setup process:

1. **Install Docker** on your machine
2. **Clone the repository**
3. **Login to GitLab Container Registry**:
   ```bash
   docker login registry.gitlab.wethinkco.de
   ```
4. **Pull and run the image**:
   ```bash
   docker pull registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:latest
   docker run -d --name robot-worlds-server -p 5050:5050 registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:latest
   ```
5. **Test the server**:
   ```bash
   curl http://localhost:5050  # or open in browser
   ```

## CI/CD Integration

The Docker image is automatically built and published when:
- Code is pushed to the main branch
- All tests pass
- The GitLab CI/CD pipeline completes successfully

The pipeline includes:
1. **docker-build**: Builds the Docker image
2. **docker-test**: Tests the Docker container
3. **docker-publish**: Publishes to GitLab Container Registry

## Cleanup

### Remove All Docker Resources
```bash
# Windows
build.bat docker-clean

# Unix/Linux/Mac
make docker-clean
```

### Manual Cleanup
```bash
# Stop and remove containers
docker stop robot-worlds-server robot-worlds-test
docker rm robot-worlds-server robot-worlds-test

# Remove images
docker rmi robot-worlds-server:2.0.0 robot-worlds-server:latest

# Clean up system
docker system prune
```
