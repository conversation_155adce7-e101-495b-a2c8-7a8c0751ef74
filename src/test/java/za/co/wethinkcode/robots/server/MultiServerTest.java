package za.co.wethinkcode.robots.server;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.junit.jupiter.api.*;
import java.io.*;
import java.net.Socket;
import java.net.SocketException;
import java.net.UnknownHostException;
import static org.junit.jupiter.api.Assertions.assertEquals;
import za.co.wethinkcode.robots.config.Config;

class MultiServerTest {
    private int testPort;  // <-- changed from static final to dynamic port
    private Thread serverThread;

    @BeforeEach
    void setupServer() {
        // Pick a free port
        try (java.net.ServerSocket socket = new java.net.ServerSocket(0)) {
            testPort = socket.getLocalPort();
        } catch (IOException e) {
            throw new RuntimeException("Failed to find free port", e);
        }

        // Start server with the picked port
        serverThread = new Thread(() -> {
            try {
                // Set test mode system property for this test
                System.setProperty("test.mode", "true");
                MultiServers.main(new String[]{"-p", String.valueOf(testPort)});
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        serverThread.start();

        waitForServerToBeReady();
    }

    @AfterEach
    void tearDownServer() throws IOException {
        MultiServerEngine server = MultiServers.getServer();
        if (server != null) {
            server.shutdown();
        }

        if (serverThread != null) {
            serverThread.interrupt();
        }
    }

    @Test
    @DisplayName("Test server setup and response")
    void testServerSetupAndResponse() throws IOException {
        try (Socket clientSocket = new Socket("localhost", testPort);
             PrintWriter out = new PrintWriter(clientSocket.getOutputStream(), true);
             BufferedReader in = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()))) {

            JsonObject launchRequest = new JsonObject();
            launchRequest.addProperty("robot", "TestBot10");
            launchRequest.addProperty("command", "launch");
            JsonArray args = new JsonArray();
            args.add("sniper");
            launchRequest.add("arguments", args);

            out.println(launchRequest);

            String response = in.readLine();
            JsonObject jsonResponse = JsonParser.parseString(response).getAsJsonObject();

            assertEquals("OK", jsonResponse.get("result").getAsString());
        }
    }

    private void waitForServerToBeReady() {
        int retries = 10;
        int delay = 500; // ms
        while (retries > 0) {
            try (Socket socket = new Socket("localhost", testPort)) {
                return;
            } catch (UnknownHostException | SocketException e) {
                retries--;
                try {
                    Thread.sleep(delay);
                } catch (InterruptedException ignored) {}
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        throw new IllegalStateException("Server did not start within the expected time.");
    }

    @Test
    @DisplayName("Test fire command after launching robot")
    void testFireCommand() throws IOException {
        try (Socket clientSocket = new Socket("localhost", testPort);
             PrintWriter out = new PrintWriter(clientSocket.getOutputStream(), true);
             BufferedReader in = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()))) {

            // Launch a robot
            JsonObject launchRequest = new JsonObject();
            launchRequest.addProperty("robot", "TestShooter");
            launchRequest.addProperty("command", "launch");
            JsonArray args = new JsonArray();
            args.add("sniper");
            launchRequest.add("arguments", args);
            out.println(launchRequest);
            in.readLine(); // consume launch response

            // Fire command
            JsonObject fireRequest = new JsonObject();
            fireRequest.addProperty("robot", "TestShooter");
            fireRequest.addProperty("command", "fire");
            fireRequest.add("arguments", new JsonArray());
            out.println(fireRequest);

            String fireResponse = in.readLine();
            JsonObject jsonResponse = JsonParser.parseString(fireResponse).getAsJsonObject();

            assertEquals("OK", jsonResponse.get("result").getAsString());
            JsonObject data = jsonResponse.getAsJsonObject("data");
            Assertions.assertTrue(data.has("message"));
            String msg = data.get("message").getAsString();
            Assertions.assertTrue(
                    msg.equals("Hit") || msg.equals("Miss") || msg.equals("Out of ammo."),
                    "Unexpected fire result message: " + msg
            );
        }
    }
}
