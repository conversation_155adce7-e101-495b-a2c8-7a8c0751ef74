package acceptance_test.WorldGui;

import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.world.World;
import za.co.wethinkcode.robots.world.WorldGUI;

import java.util.function.BooleanSupplier;

import static org.junit.jupiter.api.Assertions.*;

public class WindowCreationTest {
    @Test
    void windowCreation(){
        World testworld=new World(true);
        WorldGUI guiworld=new WorldGUI(testworld);
        assertTrue(guiworld.isVisible());
    }

    @Test
    void checkTitle(){
        World testworld=new World(true);
        WorldGUI guiworld=new WorldGUI(testworld);
        assertEquals("Toy Robot", guiworld.getTitle());
    }
}
