package acceptance_test.Moveforward;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Position;
import za.co.wethinkcode.robots.config.Config;
import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.robot.Robot;
import za.co.wethinkcode.robots.world.World;
import org.json.JSONObject;

import java.lang.reflect.Field;

import static org.junit.jupiter.api.Assertions.*;

public class MoveForwardAcceptanceTest {
    private World world;
    private Robot testRobot;

    @BeforeEach
    void setUp() throws Exception {
        // Set configuration for a 2x2 world
        setConfigField("HEIGHT", 2);
        setConfigField("WIDTH", 2);
        setConfigField("OBSTACLE_MODE", "none"); // No obstacles

        // Initialize world with GUI disabled
        world = new World(false);

        // Set test-specific Maze with no obstacles
        Maze testMaze = new Maze("none");
        assertEquals(0, testMaze.getObstacles().size(), "Test Maze should have no obstacles");
        setField(world, "maze", testMaze);
        setField(world, "obstacleList", testMaze.getObstacles());

        // Add robot at (0,0)
        testRobot = createTestRobot("HAL", new Position(0, 0));
        world.addRobot(testRobot);
        world.setCurrentRobot(testRobot);
    }

    // Helper method to set Config fields using reflection
    private void setConfigField(String fieldName, Object value) throws Exception {
        Field field = Config.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    // Helper method to set World fields using reflection
    private void setField(Object target, String fieldName, Object value) throws Exception {
        Field field = World.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    // Helper method to create a Robot for testing
    private Robot createTestRobot(String name, Position position) {
        Robot robot = new Robot(name, "soldier");
        robot.setPosition(position);
        return robot;
    }

    @Test
    @DisplayName("AT-9.1: Test robot move forward")
    void testMoveForward() {
        // Given a robot is at position (0,0) in a 2x2 world
        assertEquals(new Position(0, 0), testRobot.getPosition(), "Robot should be at (0,0)");
        assertEquals(testRobot, world.getCurrentRobot(), "Current robot should be HAL");

        // When the robot is commanded to move forward 1 step
        JSONObject command = new JSONObject();
        command.put("command", "forward");
        command.put("robot", "HAL");
        command.put("steps", 1);
        String response = world.handleCommand(command.toString());

        // Then the robot should move to (0,1)
        JSONObject result = new JSONObject(response);
        assertEquals("OK", result.getString("result"), "Response should be OK");
        JSONObject data = result.getJSONObject("data");
        assertEquals(0, data.getInt("x"), "Robot should not change x-coordinate");
        assertEquals(1, data.getInt("y"), "Robot should move to y=1");
        assertTrue(result.getString("message").contains("Moved forward"), "Message should indicate successful move");

        // And verify position in world
        assertEquals(new Position(0, 1), testRobot.getPosition(), "Robot position should be (0,1)");
        assertTrue(world.isNewPositionAllowed(new Position(0, 1)), "Position (0,1) should be valid");

        // Acceptance Criteria
        assertTrue(data.getInt("y") == 1, "Robot moves correctly in open space");
        assertFalse(result.getString("message").contains("edge"), "Message should not indicate edge");
        assertEquals("OK", result.getString("result"), "Response format is correct");
    }

    @Test
    @DisplayName("AT-9.1: Test robot move forward at edge")
    void testMoveForwardAtEdge() throws Exception {
        // Reconfigure for a 1x1 world
        setConfigField("HEIGHT", 1);
        setConfigField("WIDTH", 1);
        setConfigField("OBSTACLE_MODE", "none");
        world = new World(false);
        Maze testMaze = new Maze("none");
        setField(world, "maze", testMaze);
        setField(world, "obstacleList", testMaze.getObstacles());
        testRobot = createTestRobot("HAL", new Position(0, 0));
        world.addRobot(testRobot);
        world.setCurrentRobot(testRobot);

        // Given a robot is at position (0,0) in a 1x1 world
        assertEquals(new Position(0, 0), testRobot.getPosition(), "Robot should be at (0,0)");
        assertEquals(testRobot, world.getCurrentRobot(), "Current robot should be HAL");

        // When the robot is commanded to move forward 5 steps
        JSONObject command = new JSONObject();
        command.put("command", "forward");
        command.put("robot", "HAL");
        command.put("steps", 5);
        String response = world.handleCommand(command.toString());

        // Then the robot should stay at (0,0)
        JSONObject result = new JSONObject(response);
        assertEquals("OK", result.getString("result"), "Response should be OK");
        JSONObject data = result.getJSONObject("data");
        assertEquals(0, data.getInt("x"), "Robot should not change x-coordinate");
        assertEquals(0, data.getInt("y"), "Robot should stay at y=0");
        assertEquals("At the NORTH edge", result.getString("message"), "Message should indicate edge");

        // And verify position in world
        assertEquals(new Position(0, 0), testRobot.getPosition(), "Robot position should remain (0,0)");
        assertFalse(world.isNewPositionAllowed(new Position(0, 1)), "Position (0,1) should be invalid");

        // Acceptance Criteria
        assertEquals(0, data.getInt("y"), "Robot stays at edge");
        assertEquals("At the NORTH edge", result.getString("message"), "Message indicates edge");
        assertEquals("OK", result.getString("result"), "Response format is correct");
    }
}