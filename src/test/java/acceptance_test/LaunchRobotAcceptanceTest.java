package acceptance_test;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Position;
import za.co.wethinkcode.robots.config.Config;
import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.robot.Robot;
import za.co.wethinkcode.robots.world.World;
import org.json.JSONObject;

import java.lang.reflect.Field;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static acceptance_test.launchhelpermethods.LaunchTestConstants.*;
import static acceptance_test.launchhelpermethods.LaunchRequestFactory.createValidLaunchRequest;

public class LaunchRobotAcceptanceTest {
    private World world;

    @BeforeEach
    void setUp() throws Exception {
        // Set configuration for a 2x2 world with one obstacle at (1,1)
        setConfigField("HEIGHT", 2);
        setConfigField("WIDTH", 2);
        setConfigField("OBSTACLE_MODE", "M-1,1:1,1"); // Mountain at (1,1)

        // Initialize world with GUI disabled
        world = new World(false);

        // Set test-specific Maze with one obstacle
        Maze testMaze = new Maze("M-1,1:1,1");
        assertEquals(1, testMaze.getObstacles().size(), "Test Maze should have one obstacle");
        setField(world, "maze", testMaze);
        setField(world, "obstacleList", testMaze.getObstacles());
    }

    // Helper method to set Config fields using reflection
    private void setConfigField(String fieldName, Object value) throws Exception {
        Field field = Config.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    // Helper method to set World fields using reflection
    private void setField(Object target, String fieldName, Object value) throws Exception {
        Field field = World.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    // Helper method to check if a position is valid (unoccupied and not an obstacle)
    private boolean isValidPosition(World world, Position position, Robot currentRobot) {
        return world.isNewPositionAllowed(position) &&
                world.getBots().stream()
                        .filter(robot -> !robot.equals(currentRobot)) // Exclude the current robot
                        .noneMatch(robot -> robot.getPosition().equals(position));
    }

    @Test
    @DisplayName("AT-10.1.1: Test robot launches in random unoccupied position")
    void testRobotLaunchesInRandomUnoccupiedPosition() {
        // Given a 2x2 world with a mountain at (1,1)
        assertEquals(1, world.getObstacles().size(), "World should have one obstacle");
        assertTrue(world.getBots().isEmpty(), "World should have no robots initially");

        // When launching a robot
        JsonObject command = createValidLaunchRequest(SOLDIER_TYPE, "Robot1");
        System.out.println("Sending launch command: " + command);
        String response = world.handleCommand(command.toString());

        // Then the robot should launch successfully in an unoccupied position
        JSONObject result = new JSONObject(response);
        System.out.println("Response: " + result);
        assertEquals("OK", result.getString("result"), "Launch should be successful");
        JSONObject data = result.getJSONObject("data");
        assertTrue(data.has("x") && data.has("y"), "Response should contain position");
        assertTrue(result.getString("message").contains("launched"), "Message should indicate successful launch");

        // Verify robot position
        Robot launchedRobot = world.getCurrentRobot();
        assertNotNull(launchedRobot, "Robot should be set as current robot");
        Position position = launchedRobot.getPosition();
        assertNotNull(position, "Robot should have a position");
        assertTrue(isValidPosition(world, position, launchedRobot), "Robot should be in a valid position");
        assertFalse(position.equals(new Position(1, 1)), "Robot should not be at obstacle position (1,1)");

        // Acceptance Criteria
        assertEquals(1, world.getBots().size(), "World should contain one robot");
        assertTrue(result.getString("message").contains("Robot 'Robot1'"),
                "Message should contain robot name");
    }

    @Test
    @DisplayName("AT-10.1.2: Test no robots launch in occupied or obstacle positions")
    void testNoLaunchInOccupiedOrObstaclePositions() {
        // Given a 2x2 world with a mountain at (1,1) and one robot
        JsonObject command1 = createValidLaunchRequest(SOLDIER_TYPE, "Robot1");
        System.out.println("Sending first launch command: " + command1);
        String response1 = world.handleCommand(command1.toString());
        System.out.println("First response: " + response1);
        assertEquals("OK", new JSONObject(response1).getString("result"), "First launch should be successful");

        Robot firstRobot = world.getCurrentRobot();
        assertNotNull(firstRobot, "First robot should be launched");
        Position firstPosition = firstRobot.getPosition();
        assertTrue(isValidPosition(world, firstPosition, firstRobot), "First robot should be in a valid position");
        assertFalse(firstPosition.equals(new Position(1, 1)), "First robot should not be at obstacle (1,1)");

        // When launching a second robot
        JsonObject command2 = createValidLaunchRequest(SNIPER_TYPE, "Robot2");
        System.out.println("Sending second launch command: " + command2);
        String response2 = world.handleCommand(command2.toString());
        System.out.println("Second response: " + response2);

        // Then the second robot should launch in a different valid position
        JSONObject result = new JSONObject(response2);
        assertEquals("OK", result.getString("result"), "Second launch should be successful");
        Robot secondRobot = world.getCurrentRobot();
        assertNotNull(secondRobot, "Second robot should be set as current robot");
        Position secondPosition = secondRobot.getPosition();
        assertNotNull(secondPosition, "Second robot should have a position");
        assertTrue(isValidPosition(world, secondPosition, secondRobot), "Second robot should be in a valid position");
        assertFalse(secondPosition.equals(new Position(1, 1)), "Second robot should not be at obstacle (1,1)");
        assertFalse(secondPosition.equals(firstPosition), "Second robot should not be at first robot's position");

        // Acceptance Criteria
        assertEquals(2, world.getBots().size(), "World should contain two robots");
        assertNotEquals(firstPosition, secondPosition, "Robots should have different positions");
    }

    @Test
    @DisplayName("AT-10.1.3: Test error response when world is full")
    void testErrorResponseWhenWorldIsFull() {
        // Given a 2x2 world with a mountain at (1,1) and robots occupying all other positions
        List<Position> validPositions = List.of(new Position(0, 0), new Position(0, 1), new Position(1, 0));
        for (int i = 0; i < validPositions.size(); i++) {
            JsonObject command = createValidLaunchRequest(SOLDIER_TYPE, "Robot" + (i + 1));
            System.out.println("Sending launch command " + (i + 1) + ": " + command);
            String response = world.handleCommand(command.toString());
            System.out.println("Response " + (i + 1) + ": " + response);
            JSONObject result = new JSONObject(response);
            assertEquals("OK", result.getString("result"),
                    "Launch " + (i + 1) + " should be successful");
            Robot robot = world.getCurrentRobot();
            assertNotNull(robot, "Robot " + (i + 1) + " should be launched");
            assertTrue(isValidPosition(world, robot.getPosition(), robot),
                    "Robot " + (i + 1) + " should be in a valid position");
        }
        assertEquals(3, world.getBots().size(), "World should have three robots");

        // When attempting to launch another robot
        JsonObject command = createValidLaunchRequest(HITBOT_TYPE, "Robot4");
        System.out.println("Sending launch command for full world: " + command);
        String response = world.handleCommand(command.toString());
        System.out.println("Full world response: " + response);

        // Then an error response should be returned
        JSONObject result = new JSONObject(response);
        assertEquals("ERROR", result.getString("result"), "Launch should fail when world is full");
        JSONObject data = result.getJSONObject("data");
        assertTrue(result.getString("message").contains("No space"),
                "Message should indicate world is full");

        // Acceptance Criteria
        assertEquals(3, world.getBots().size(), "World should still contain three robots");
    }
}