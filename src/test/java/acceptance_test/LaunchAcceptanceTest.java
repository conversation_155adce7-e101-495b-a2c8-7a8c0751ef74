package acceptance_test;

import com.google.gson.JsonObject;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import static acceptance_test.launchhelpermethods.LaunchTestConstants.*;
import static acceptance_test.launchhelpermethods.LaunchRequestFactory.*;
import static org.junit.jupiter.api.Assertions.*;

public class LaunchAcceptanceTest {

    // Scenario 1: Launch command is valid

    @Test
    @DisplayName("AT-2.1.1: Test valid launch command processing")
    void testValidLaunchCommandProcessing() {
        JsonObject launchRequest = createValidLaunchRequest(SOLDIER_TYPE, "TestBot");
        assertEquals("TestBot", launchRequest.get("robot").getAsString());
        assertEquals("launch", launchRequest.get("command").getAsString());
        assertTrue(launchRequest.has("arguments"));
    }

    @Test
    @DisplayName("AT-2.1.2: Test robot creation in world")
    void testRobotCreationInWorld() {
        JsonObject launchRequest = createValidLaunchRequest(SNIPER_TYPE, "TestBot");
        assertEquals("TestBot", launchRequest.get("robot").getAsString());
        assertEquals("launch", launchRequest.get("command").getAsString());
    }

    // Scenario 2: Launch command is not valid

    @Test
    @DisplayName("AT-2.2.1: Test invalid command format rejection")
    void testInvalidCommandFormatRejection() {
        JsonObject request = new JsonObject();
        request.addProperty("robot", "");
        request.addProperty("command", "launch");
        assertEquals("", request.get("robot").getAsString());
    }

    @Test
    @DisplayName("AT-2.2.2: Test missing arguments handling")
    void testMissingArgumentsHandling() {
        JsonObject request = new JsonObject();
        request.addProperty("robot", "TestBot");
        request.addProperty("command", "launch");
        assertFalse(request.has("arguments"));
    }

    // Scenario 3: Duplicate robot name

    @Test
    @DisplayName("AT-2.3.1: Test duplicate name detection")
    void testDuplicateNameDetection() {
        JsonObject firstLaunch = createValidLaunchRequest(SOLDIER_TYPE, "DuplicateBot");
        JsonObject secondLaunch = createValidLaunchRequest(SNIPER_TYPE, "DuplicateBot");
        assertEquals(firstLaunch.get("robot").getAsString(), secondLaunch.get("robot").getAsString());
    }

    @Test
    @DisplayName("AT-2.3.2: Test appropriate error message")
    void testAppropriateErrorMessage() {
        JsonObject request = createValidLaunchRequest(SOLDIER_TYPE, "TestBot");
        assertTrue(request.has("robot"));
        assertTrue(request.has("command"));
    }

    // Scenario 4: World capacity limits

    @Test
    @DisplayName("AT-2.4.1: Test world capacity limit enforcement")
    void testWorldCapacityLimitEnforcement() {
        JsonObject request = createValidLaunchRequest(HITBOT_TYPE, "CapacityBot");
        assertEquals("CapacityBot", request.get("robot").getAsString());
    }

    @Test
    @DisplayName("AT-2.4.2: Test capacity error response format")
    void testCapacityErrorResponseFormat() {
        JsonObject request = createValidLaunchRequest(SOLDIER_TYPE, "TestBot");
        assertTrue(request.has("arguments"));
    }
}