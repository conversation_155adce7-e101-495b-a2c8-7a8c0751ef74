package acceptance_test.WorldTests;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Position;
import za.co.wethinkcode.robots.config.Config;
import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.obstacle.Obstacle;
import za.co.wethinkcode.robots.obstacle.ObstacleType;
import za.co.wethinkcode.robots.robot.Robot;
import za.co.wethinkcode.robots.world.World;
import za.co.wethinkcode.robots.world.WorldGUI;

import java.lang.reflect.Field;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

// Test class for position validation acceptance tests
public class WorldPositionValidationAcceptanceTest {

    private World world;
    private Robot testRobot;

    // Setup method to initialize the world and robot
    @BeforeEach
    void setUp() throws Exception {
        // Set configuration before World init
        setConfigField("HEIGHT", 20);
        setConfigField("WIDTH", 20);
        setConfigField("OBSTACLE_MODE", "BP-10,10:11,11"); // Single bottomless pit

        // Initialize world with GUI disabled in headless mode
        boolean enableGUI = !java.awt.GraphicsEnvironment.isHeadless();
        world = new World(enableGUI);

        // Set test-specific Maze to ensure single obstacle
        Maze testMaze = new Maze("BP-10,10:11,11");
        assertEquals(1, testMaze.getObstacles().size(), "Test Maze should have one obstacle");
        setField(world, "maze", testMaze);
        setField(world, "obstacleList", testMaze.getObstacles());

        // Add robot at (10,10)
        testRobot = createTestRobot("TestBot", new Position(10, 10));
        world.addRobot(testRobot);
        world.setCurrentRobot(testRobot);
    }

    // Helper method to set Config fields using reflection
    private void setConfigField(String fieldName, Object value) throws Exception {
        Field field = Config.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    // Helper method to set World fields using reflection
    private void setField(Object target, String fieldName, Object value) throws Exception {
        Field field = World.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    // Helper method to create a Robot for testing
    private Robot createTestRobot(String name, Position position) {
        Robot robot = new Robot(name, "soldier"); // Use soldier type
        robot.setPosition(position); // Set position after creation
        return robot;
    }

    // ========================================
    // Scenario c: Position validation for movement
    // ========================================
    @Test
    @DisplayName("AT-5.1.3: Test position validation for robot movement")
    void testPositionValidation() {
        // Given a robot is at position (10,10)
        assertEquals(new Position(10, 10), testRobot.getPosition(), "Robot should be at (10,10)");
        assertEquals(testRobot, world.getCurrentRobot(), "Current robot should be TestBot");

        // And the robot wants to move to position (12,10)
        Position newPosition = new Position(12, 10);

        // When the world validates the new position
        boolean isValid = world.isNewPositionAllowed(newPosition);

        // Then the world should check for other robots at that position
        List<Robot> robots = world.getBots();
        boolean noOtherRobots = robots.stream()
                .noneMatch(robot -> !robot.equals(testRobot) && robot.getPosition().equals(newPosition));
        assertTrue(noOtherRobots, "No other robots should be at (12,10)");

        // And the world should check for blocking obstacles
        List<Obstacle> obstacles = world.getMaze().getObstacles();
        boolean noBlockingObstacles = obstacles.stream()
                .noneMatch(obstacle -> obstacle.getType() != ObstacleType.BOTTOMLESS_PIT
                        && (obstacle.blocksPosition(newPosition) || obstacle.blocksPath(testRobot.getPosition(), newPosition)));
        assertTrue(noBlockingObstacles, "No blocking obstacles (mountains or lakes) should be at (12,10) or on path");

        // And the world should return true if the position is valid
        assertTrue(isValid, "Position (12,10) should be valid: within bounds, no other robots, no blocking obstacles");
        assertTrue(newPosition.isIn(world.getTOP_LEFT(), world.getBOTTOM_RIGHT()),
                "Position (12,10) should be within world boundaries (0,0 to 19,19)");
    }
}