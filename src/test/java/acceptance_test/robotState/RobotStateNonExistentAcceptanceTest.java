package acceptance_test.robotState;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Position;
import za.co.wethinkcode.robots.config.Config;
import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.world.World;

import java.lang.reflect.Field;

import static org.junit.jupiter.api.Assertions.*;

// Test class for state request for non-existent robot acceptance tests
public class RobotStateNonExistentAcceptanceTest {

    private World world;

    // Setup method to initialize the world
    @BeforeEach
    void setUp() throws Exception {
        // Set configuration before World init
        setConfigField("HEIGHT", 20);
        setConfigField("WIDTH", 20);
        setConfigField("OBSTACLE_MODE", "BP-10,10:11,11"); // Single bottomless pit
        setConfigField("MAX_SHOTS", 3);
        setConfigField("MAX_SHIELD", 3);

        // Initialize world with GUI enabled
        world = new World(false);

        // Set test-specific Maze to ensure single obstacle
        Maze testMaze = new Maze("BP-10,10:11,11");
        assertEquals(1, testMaze.getObstacles().size(), "Test Maze should have one obstacle");
        setField(world, "maze", testMaze);
        setField(world, "obstacleList", testMaze.getObstacles());
    }

    // Helper method to set Config fields using reflection
    private void setConfigField(String fieldName, Object value) throws Exception {
        Field field = Config.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    // Helper method to set World fields using reflection
    private void setField(Object target, String fieldName, Object value) throws Exception {
        Field field = World.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    // ========================================
    // Scenario b: State requested for a non-existent robot
    // ========================================
    @Test
    @DisplayName("AT-4.1.2: Test state request for a non-existent robot")
    void testNonExistentRobotStateRequest() {
        // Given no robot with that name exists in the world
        assertTrue(world.getBots().isEmpty(), "World should have no robots");

        // When I send a state command with an unknown name
        world.setCurrentRobotByName("UnknownBot");

        // Then I should receive an error message indicating the robot does not exist
        assertNull(world.getCurrentRobot(), "Current robot should be null for non-existent robot name");
        // Note: No explicit error message in World; assumes server would return "Robot does not exist"
    }
}