package acceptance_test.clienthelpermethods;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import za.co.wethinkcode.robots.client.Client;
import java.lang.reflect.Method;
import static org.junit.jupiter.api.Assertions.*;
import static acceptance_test.clienthelpermethods.TestConstants.*;

public class JsonValidator {

    public static class JsonFields {
        public static final String ROBOT = "robot";
        public static final String COMMAND = "command";
        public static final String ARGUMENTS = "arguments";

        public static String[] getRequiredFields() {
            return new String[]{ROBOT, COMMAND, ARGUMENTS};
        }
    }

    public static class CommandRequest {
        private final String command;
        private final String robotName;
        private final String[] arguments;

        public CommandRequest(String command, String robotName, String[] arguments) {
            this.command = command;
            this.robotName = robotName;
            this.arguments = arguments;
        }

        public String getCommand() { return command; }
        public String getRobotName() { return robotName; }
        public String[] getArguments() { return arguments; }
    }

    public static void validateJsonFormat(String command, String expectedCommand, String[] arguments) {
        assertDoesNotThrow(() -> {
            JsonObject request = buildJsonCommandUsingReflection(command, TEST_ROBOT_NAME);
            CommandRequest expected = new CommandRequest(expectedCommand, TEST_ROBOT_NAME, arguments);
            validateJsonRequestStructure(request, expected);
        });
    }

    public static JsonObject buildJsonCommandUsingReflection(String command, String robotName) throws Exception {
        Method buildJsonMethod = Client.class.getDeclaredMethod("buildJsonCommand", String.class, String.class);
        buildJsonMethod.setAccessible(true);
        return (JsonObject) buildJsonMethod.invoke(null, command, robotName);
    }

    public static void validateJsonRequestStructure(JsonObject request, CommandRequest expected) {
        for (String field : JsonFields.getRequiredFields()) {
            assertTrue(request.has(field));
        }
        assertTrue(request.get(JsonFields.ARGUMENTS).isJsonArray());
        assertEquals(3, request.size());

        assertEquals(expected.getCommand(), request.get(JsonFields.COMMAND).getAsString());
        assertEquals(expected.getRobotName(), request.get(JsonFields.ROBOT).getAsString());

        JsonArray args = request.get(JsonFields.ARGUMENTS).getAsJsonArray();
        assertEquals(expected.getArguments().length, args.size());

        for (int i = 0; i < expected.getArguments().length; i++) {
            validateArgument(args.get(i), expected.getArguments()[i]);
        }
    }

    public static void validateRequiredFields(JsonObject request) {
        for (String field : JsonFields.getRequiredFields()) {
            assertTrue(request.has(field));
        }
        assertTrue(request.get(JsonFields.ARGUMENTS).isJsonArray());
    }

    public static void validateFieldValues(JsonObject request, String expectedCommand, String expectedRobotName) {
        assertEquals(expectedCommand, request.get(JsonFields.COMMAND).getAsString());
        assertEquals(expectedRobotName, request.get(JsonFields.ROBOT).getAsString());
    }

    public static void validateArguments(JsonObject request, String[] expectedArguments) {
        JsonArray args = request.get(JsonFields.ARGUMENTS).getAsJsonArray();
        assertEquals(expectedArguments.length, args.size());
        for (int i = 0; i < expectedArguments.length; i++) {
            validateArgument(args.get(i), expectedArguments[i]);
        }
    }

    private static void validateArgument(JsonElement arg, String expectedArgument) {
        try {
            int expectedInt = Integer.parseInt(expectedArgument);
            assertEquals(expectedInt, arg.getAsInt());
        } catch (NumberFormatException e) {
            assertEquals(expectedArgument, arg.getAsString());
        }
    }

    public static void validateSingleArgument(JsonElement arg, String expectedArgument, int index) {
        validateArgument(arg, expectedArgument);
    }

    public static void validateJsonStructure(JsonObject request) {
        assertEquals(3, request.size());
    }
}
