package acceptance_test.clienthelpermethods;

import java.net.*;
import static org.junit.jupiter.api.Assertions.*;
import static acceptance_test.clienthelpermethods.TestConstants.*;

/**
 * Manager for handling network connections in tests
 */
public class ConnectionManager {

    /**
     * Creates a new socket connection to the test server
     */

    public static Socket createConnection() throws Exception {
        Socket socket = new Socket();
        socket.connect(new InetSocketAddress(TEST_HOST, TEST_PORT), TIMEOUT);
        return socket;
    }

    /**
     * Validates that a socket connection is properly established
     */

    public static void validateConnection(Socket currentSocket, String message) {
        assertTrue(currentSocket.isConnected(), message);
        assertFalse(currentSocket.isClosed(), "Socket should not be closed");
    }

    /**
     * Validates that socket streams are available
     */

    public static void validateStreams(Socket currentSocket) throws Exception {
        assertNotNull(currentSocket.getInputStream(), "Input stream should be available");
        assertNotNull(currentSocket.getOutputStream(), "Output stream should be available");
    }

    /**
     * Logs successful connection details to console
     */

    public static void logConnectionSuccess() {
        System.out.println("Connection established successfully");
        System.out.println("Host: " + TEST_HOST);
        System.out.println("Port: " + TEST_PORT);
        System.out.println("Status: Connected");
    }
}
