package acceptance_test.clienthelpermethods;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Validator for output validation in acceptance tests
 */

public class OutputValidator {

    /**
     * Validates success response output for robot commands
     */

    public static void validateSuccessResponseOutput(String output, String expectedMessage, String command) {
        assertTrue(output.contains(command + " command handled successfully"),
            "Success message should be displayed for " + command);
        assertTrue(output.contains("Result: OK"),
            "Result should be OK for " + command);
        assertTrue(output.contains(expectedMessage),
            "Expected message should be displayed for " + command);
        assertTrue(output.contains("Server Command>"),
            "Client should be ready for next command after " + command);
    }

    /**
     * Validates connection confirmation messages
     */
    public static void validateConnectionMessage(String output) {
        assertTrue(output.contains("Connected to the server"), 
            "Confirmation message should be displayed");
        assertTrue(output.contains("Initializing client robot"), 
            "Initialization message should be displayed");
        assertFalse(output.trim().isEmpty(), 
            "Output should not be empty");
    }

    /**
     * Validates robot launch prompt display
     */

    public static void validateRobotLaunchPrompt(String output) {
        assertTrue(output.contains("launch <robottype> <robotname>"), 
            "should show the launch command format");
        assertTrue(output.contains("Launch a robot using the following"), 
            "Launch prompt should be displayed");
        assertTrue(output.contains("Robot types:"), 
            "Robot types should be displayed");
        assertTrue(output.contains("Sniper") && output.contains("Soldier") && output.contains("Hitbot"), 
            "All robot types should be listed");

        int connectionIndex = output.indexOf("Connected to the server");
        int launchIndex = output.indexOf("Launch a robot using the following");
        assertTrue(connectionIndex < launchIndex && connectionIndex != -1 && launchIndex != -1, 
            "Launch prompt should appear after connection message");
    }

    public static void validateRobotStateDisplay(String output) {
        assertTrue(output.contains("State:"),
                "State should be displayed");
        assertTrue(output.contains("Position:"),
                "Position should be displayed");
        assertTrue(output.contains("Direction:"),
                "Direction should be displayed");
        assertTrue(output.contains("Make:"),
                "Make should be displayed");
        assertTrue(output.contains("Shots:"),
                "Shots should be displayed");
        assertTrue(output.contains("Shields:"),
                "Shields should be displayed");
        assertTrue(output.contains("Status:"),
                "Status should be displayed");
        assertTrue(output.contains("Ready for next command"),
                "Client should be ready for next command");
    }

    public static void validateNextCommandPrompt(String output) {
        assertTrue(output.contains("Enter command:"),
                "Next command prompt should be displayed");
        assertFalse(output.trim().isEmpty(),
                "Output should not be empty");
        assertTrue(output.contains(">"),
                "Command prompt indicator should be present");
    }
}
