package acceptance_test.clienthelpermethods;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import static acceptance_test.clienthelpermethods.TestConstants.*;

/**
 * Factory for creating JSON response objects
 */
public class JsonResponseFactory {

    public static JsonObject createLaunchSuccessResponse() {
        JsonObject response = new JsonObject();
        response.addProperty("result", "OK");

        JsonObject data = new JsonObject();
        data.addProperty("message", "Robot '" + TEST_ROBOT_NAME + "' of type 'soldier' launched.");
        response.add("data", data);

        response.add("state", createRobotState());
        return response;
    }

    public static JsonObject createMoveSuccessResponse() {
        return createBasicSuccessResponse("Done");
    }

    public static JsonObject createTurnSuccessResponse() {
        return createBasicSuccessResponse("Done");
    }
    public static JsonObject createLookSuccessResponse() {
        JsonObject response = new JsonObject();
        response.addProperty("result", "OK");

        JsonObject data = new JsonObject();
        JsonArray objects = new JsonArray();
        JsonObject edge = new JsonObject();

        edge.addProperty("type", "EDGE");
        edge.addProperty("direction", "NORTH");
        edge.addProperty("distance", 10);
        objects.add(edge);

        data.add("objects", objects);
        response.add("data", data);

        return response;
    }

    public static JsonObject createFireSuccessResponse(String message) {
        return createBasicSuccessResponse(message);
    }

    public static JsonObject createBasicSuccessResponse(String message) {
        JsonObject response = new JsonObject();
        response.addProperty("result", "OK");

        JsonObject data = new JsonObject();
        data.addProperty("message", message);
        response.add("data", data);

        return response;
    }

    /**
     * Creates a robot state object for responses
     */

    public static JsonObject createRobotState() {
        JsonObject state = new JsonObject();
        JsonArray position = new JsonArray();
        position.add(0);
        position.add(0);
        state.add("position", position);
        state.addProperty("make", "soldier");
        state.addProperty("direction", "NORTH");
        state.addProperty("shields", 3);
        state.addProperty("shots", 3);
        state.addProperty("status", "NORMAL");
        return state;
    }
}
