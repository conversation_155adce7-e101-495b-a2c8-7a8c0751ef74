package acceptance_test.clienthelpermethods;

import com.google.gson.JsonObject;
import za.co.wethinkcode.robots.client.Client;
import java.io.*;
import java.net.Socket;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Executor for running test scenarios and capturing output
 */

public class TestExecutor {

    /**
     * Executes a connection test with proper exception handling
     */

    public static void executeConnectionTest(ConnectionTestAction action) {
        assertDoesNotThrow(() -> {
            try (Socket socket = ConnectionManager.createConnection()) {
                action.execute();
            }
        }, "Connection should be established without throwing exceptions");
    }
    
    /**
     * Executes a test action while capturing console output for validation
     */

    public static void executeWithOutputCapture(Runnable testAction, OutputValidationAction outputValidator) {
        ByteArrayOutputStream outContent = new ByteArrayOutputStream();
        PrintStream originalOut = System.out;
        
        try {
            System.setOut(new PrintStream(outContent));
            testAction.run();
            outputValidator.validate(outContent.toString());
        } finally {
            System.setOut(originalOut);
        }
    }

    /**
     * Tests success response handling for robot commands
     */
    public static void testSuccessResponse(String command, JsonObject response, String expectedMessage) {
        executeWithOutputCapture(() -> {
            String result = response.get("result").getAsString();

            if ("OK".equals(result)) {
                System.out.println(command + " command handled successfully");
                String formatted = Client.formatServerResponse(response);
                System.out.print(formatted);
            }
        }, output -> {
            OutputValidator.validateSuccessResponseOutput(output, expectedMessage, command);
        });
    }
    
    /**
     * Functional interface for connection test actions
     */

    @FunctionalInterface
    public interface ConnectionTestAction {
        void execute() throws Exception;
    }

    /**
     * Functional interface for output validation actions
     */

    @FunctionalInterface
    public interface OutputValidationAction {
        void validate(String output);
    }
}
