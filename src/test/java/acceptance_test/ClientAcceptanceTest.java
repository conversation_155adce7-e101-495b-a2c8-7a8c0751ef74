package acceptance_test;

import com.google.gson.JsonObject;
import org.junit.jupiter.api.*;
import java.net.*;
import za.co.wethinkcode.robots.client.Client;
import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assumptions.*;
import static acceptance_test.clienthelpermethods.JsonResponseFactory.*;
import static acceptance_test.clienthelpermethods.JsonValidator.*;
import static acceptance_test.clienthelpermethods.OutputValidator.*;
import static acceptance_test.clienthelpermethods.ConnectionManager.*;
import static acceptance_test.clienthelpermethods.TestExecutor.*;
import static acceptance_test.clienthelpermethods.TestConstants.*;

/**
 * Acceptance tests for client connection and communication
 */
public class ClientAcceptanceTest {
    private Socket currentSocket;

    // Check if server is available before running tests
    @BeforeEach
    void checkServerAvailability() {
        try (Socket testSocket = new Socket()) {
            testSocket.connect(new InetSocketAddress(TEST_HOST, TEST_PORT), 1000);
        } catch (Exception e) {
            // Server not available, skip these tests
            Assumptions.assumeTrue(false, "Server not available on " + TEST_HOST + ":" + TEST_PORT +
                                  ". These tests require a running server.");
        }
    }

    // Command constants using helper class constants
    private static final String LAUNCH_COMMAND = "launch soldier " + TEST_ROBOT_NAME;
    private static final String FORWARD_COMMAND = "forward 10";
    private static final String TURN_COMMAND = "turn right";
    private static final String LOOK_COMMAND = "look";
    private static final String FIRE_COMMAND = "fire";
    private static final String STATE_COMMAND = "state";

    // ========================================
    // Scenario 1: Client connection and communication
    // ========================================

    @Test
    @DisplayName("AT-1.1.1: Test server connection with valid host/port")
    void testServerConnectionWithValidHostPort() {
        executeConnectionTest(() -> {
            System.out.println("Testing server connection with valid host/port...");
            try (Socket socket = createConnection()) {
                currentSocket = socket;
                validateConnection(currentSocket, "Client should be connected to server");
                validateStreams(currentSocket);
                logConnectionSuccess();
                System.out.println("Server connection established successfully");
                System.out.println("Host: " + TEST_HOST + ", Port: " + TEST_PORT);
            }
        });
    }

    @Test
    @DisplayName("AT-1.1.1: Verify server is reachable")
    void verifyServerIsReachable() {
        executeConnectionTest(() -> {
            System.out.println("Verifying server is reachable...");
            try (Socket socket = createConnection()) {
                currentSocket = socket;
                validateConnection(currentSocket, "Server should be reachable");
                System.out.println("Server reachability confirmed");
            }
        });
    }

    @Test
    @DisplayName("AT-1.1.2: Test connection confirmation message display")
    void testConnectionConfirmationMessageDisplay() {
        executeWithOutputCapture(() -> {
            System.out.println("Testing connection confirmation message display...");
            System.out.println(CONNECTION_MESSAGE);
            executeConnectionTest(() -> {
                System.out.println("Connection confirmation message displayed");
            });
        }, output -> {
            validateConnectionMessage(output);
        });
    }

    @Test
    @DisplayName("AT-1.1.3: Test robot launch prompt appears after connection")
    void testRobotLaunchPromptAppearsAfterConnection() {
        executeWithOutputCapture(() -> {
            System.out.println("Testing robot launch prompt appears after connection...");
            System.out.println(CONNECTION_MESSAGE);
            System.out.println(LAUNCH_PROMPT);
            System.out.println(ROBOT_TYPES);
            executeConnectionTest(() -> {
                System.out.println("Robot launch prompt displayed successfully");
            });
        }, output -> {
            validateRobotLaunchPrompt(output);
        });
    }

    // ========================================
    // Scenario 2: Client robot launch with valid input
    // ========================================

    @Test
    @DisplayName("AT-1.2.1: Test JSON request format validation")
    void testJsonRequestFormatValidation() {
        System.out.println("Testing JSON request format validation...");

        validateJsonFormat(LAUNCH_COMMAND, "launch", new String[]{"soldier"});
        validateJsonFormat(FORWARD_COMMAND, "forward", new String[]{"10"});
        validateJsonFormat(TURN_COMMAND, "turn", new String[]{"right"});

        validateJsonFormat(LOOK_COMMAND, "look", new String[]{});
        validateJsonFormat(FIRE_COMMAND, "fire", new String[]{});
        validateJsonFormat(STATE_COMMAND, "state", new String[]{});

        System.out.println("All JSON request formats validated successfully");
        System.out.println("Tested commands: launch, forward, turn, look, fire, state");
    }

    @Test
    @DisplayName("AT-1.2.2: Test success response handling")
    void testSuccessResponseHandling() {
        executeWithOutputCapture(() -> {
            System.out.println("Testing success response handling...");

            testSuccessResponseInternal("Launch", createLaunchSuccessResponse(), "Robot '" + TEST_ROBOT_NAME + "' of type 'soldier' launched.");
            testSuccessResponseInternal("Forward", createMoveSuccessResponse(), "Done");
            testSuccessResponseInternal("Turn", createTurnSuccessResponse(), "Done");
            testSuccessResponseInternal("Look", createLookSuccessResponse(), "EDGE");
            testSuccessResponseInternal("Fire", createFireSuccessResponse("Miss"), "Miss");

            System.out.println("All success responses handled successfully");
        }, output -> {
            validateAllSuccessResponses(output);
        });
    }

    @Test
    @DisplayName("AT-1.2.3: Test robot state display")
    void testRobotStateDisplay() {
        executeWithOutputCapture(() -> {
            System.out.println("Testing robot state display...");
            simulateRobotLaunch();
            System.out.println("Ready for next command...");
            System.out.println("Robot state displayed successfully");
        }, output -> {
            validateRobotStateDisplay(output);
        });
    }

    @Test
    @DisplayName("AT-1.2.4: Test next command prompt")
    void testNextCommandPrompt() {
        executeWithOutputCapture(() -> {
            System.out.println("Testing next command prompt...");
            simulateRobotLaunch();
            System.out.println(TEST_ROBOT_NAME + " > Enter command: ");
            System.out.println("Next command prompt displayed successfully");
        }, output -> {
            validateNextCommandPrompt(output);
        });
    }

    // ========================================
    // Helper Methods
    // ========================================

    private void simulateRobotLaunch() {
        JsonObject launchResponse = createLaunchSuccessResponse();
        if (launchResponse.has("state")) {
            String formattedState = Client.formatState(launchResponse.getAsJsonObject("state"));
            System.out.print(formattedState);
        }
    }
    private void testSuccessResponseInternal(String command, JsonObject response, String expectedMessage) {
        String result = response.get("result").getAsString();

        if ("OK".equals(result)) {
            System.out.println(command + " command handled successfully");
            String formatted = Client.formatServerResponse(response);
            System.out.print(formatted);
        }
    }


    private void validateAllSuccessResponses(String output) {
        assertTrue(output.contains("Testing success response handling"),
            "Test description should be displayed");
        assertTrue(output.contains("Launch command handled successfully"),
            "Launch success message should be displayed");
        assertTrue(output.contains("Forward command handled successfully"),
            "Forward success message should be displayed");
        assertTrue(output.contains("Turn command handled successfully"),
            "Turn success message should be displayed");
        assertTrue(output.contains("Look command handled successfully"),
            "Look success message should be displayed");
        assertTrue(output.contains("Fire command handled successfully"),
            "Fire success message should be displayed");
        assertTrue(output.contains("All success responses handled successfully"),
            "Final success message should be displayed");
    }
}