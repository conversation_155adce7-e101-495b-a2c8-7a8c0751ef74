package acceptance_test.look;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Position;
import za.co.wethinkcode.robots.config.Config;
import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.obstacle.Obstacle;
import za.co.wethinkcode.robots.obstacle.ObstacleType;
import za.co.wethinkcode.robots.robot.Robot;
import za.co.wethinkcode.robots.world.World;
import org.json.JSONArray;
import org.json.JSONObject;

import java.lang.reflect.Field;

import static org.junit.jupiter.api.Assertions.*;
import static za.co.wethinkcode.robots.Direction.*;

public class LookAcceptanceTest {
    private World world;
    private Robot testRobot;

    @BeforeEach
    void setUp() throws Exception {
        // Set world config to 2x2 with no obstacles from config file
        setConfigField("HEIGHT", 2);
        setConfigField("WIDTH", 2);
        setConfigField("OBSTACLE_MODE", "none");

        // Create world and maze with no obstacles initially
        world = new World(false);
        Maze testMaze = new Maze("none");

        // Inject maze and obstacles list via reflection to the world
        setField(world, "maze", testMaze);
        setField(world, "obstacleList", testMaze.getObstacles());

        // Create test robot HAL at (0,0), facing NORTH by default
        testRobot = createTestRobot("HAL", new Position(0, 0));
        testRobot.setCurrentDirection(NORTH);
        world.addRobot(testRobot);
        world.setCurrentRobot(testRobot);

        // Add another robot BOSS at (1,0)
        Robot otherRobot = createTestRobot("BOSS", new Position(1, 0));
        world.addRobot(otherRobot);

        // Add mountain obstacle at (0,1)
        world.getObstacles().add(new Obstacle(0, 1, 0, 1, ObstacleType.MOUNTAIN));
    }

    private void setConfigField(String fieldName, Object value) throws Exception {
        Field field = Config.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    private void setField(Object target, String fieldName, Object value) throws Exception {
        Field field = World.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    private Robot createTestRobot(String name, Position position) {
        Robot robot = new Robot(name, "soldier");
        robot.setPosition(position);
        return robot;
    }

    @Test
    @DisplayName("AT-11.1a: Look detects mountain obstacle to the NORTH")
    void testLookDetectsMountainObstacleNorth() {
        // HAL is facing NORTH, mountain at (0,1)
        JSONObject command = new JSONObject()
                .put("command", "look")
                .put("robot", "HAL");

        String response = world.handleCommand(command.toString());
        System.out.println("Look NORTH Response: " + response);

        JSONObject result = new JSONObject(response);
        assertEquals("OK", result.getString("result"), "Response should be OK");

        JSONArray objects = result.getJSONObject("data").getJSONArray("objects");
        assertEquals(1, objects.length(), "Should detect exactly one object");

        JSONObject obj = objects.getJSONObject(0);
        assertEquals("mountain", obj.getString("type"), "Detected object should be a mountain");
        assertEquals(1, obj.getInt("distance"), "Mountain should be at distance 1");
    }

    @Test
    @DisplayName("AT-11.1b: Look detects robot to the EAST")
    void testLookDetectsRobotEast() {
        // Change HAL direction to EAST to see robot BOSS at (1,0)
        testRobot.setCurrentDirection(EAST);

        JSONObject command = new JSONObject()
                .put("command", "look")
                .put("robot", "HAL");

        String response = world.handleCommand(command.toString());
        System.out.println("Look EAST Response: " + response);

        JSONObject result = new JSONObject(response);
        assertEquals("OK", result.getString("result"), "Response should be OK");

        JSONArray objects = result.getJSONObject("data").getJSONArray("objects");
        assertEquals(1, objects.length(), "Should detect exactly one object");

        JSONObject obj = objects.getJSONObject(0);
        assertEquals("robot", obj.getString("type"), "Detected object should be a robot");
        assertEquals(1, obj.getInt("distance"), "Robot should be at distance 1");
    }

    @Test
    @DisplayName("AT-11.1c: Look detects nothing to the SOUTH")
    void testLookDetectsNothingSouth() {
        testRobot.setCurrentDirection(SOUTH);

        JSONObject command = new JSONObject()
                .put("command", "look")
                .put("robot", "HAL");

        String response = world.handleCommand(command.toString());
        System.out.println("Look SOUTH Response: " + response);

        JSONObject result = new JSONObject(response);
        assertEquals("OK", result.getString("result"), "Response should be OK");

        JSONArray objects = result.getJSONObject("data").getJSONArray("objects");
        assertEquals(0, objects.length(), "Should detect no objects to the SOUTH");
    }

    @Test
    @DisplayName("AT-11.1d: Look detects nothing to the WEST")
    void testLookDetectsNothingWest() {
        testRobot.setCurrentDirection(WEST);

        JSONObject command = new JSONObject()
                .put("command", "look")
                .put("robot", "HAL");

        String response = world.handleCommand(command.toString());
        System.out.println("Look WEST Response: " + response);

        JSONObject result = new JSONObject(response);
        assertEquals("OK", result.getString("result"), "Response should be OK");

        JSONArray objects = result.getJSONObject("data").getJSONArray("objects");
        assertEquals(0, objects.length(), "Should detect no objects to the WEST");
    }
}
