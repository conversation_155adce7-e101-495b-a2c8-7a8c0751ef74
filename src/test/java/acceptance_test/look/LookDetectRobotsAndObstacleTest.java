package acceptance_test.look;

import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Position;
import za.co.wethinkcode.robots.config.Config;
import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.obstacle.Obstacle;
import za.co.wethinkcode.robots.obstacle.ObstacleType;
import za.co.wethinkcode.robots.robot.Robot;
import za.co.wethinkcode.robots.world.World;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static za.co.wethinkcode.robots.Direction.NORTH;

public class LookDetectRobotsAndObstacleTest {
    private World world;
    private Robot hal;

    @BeforeEach
    void setUp() throws Exception {
        // Set world config to 2x2
        setConfig("HEIGHT", 2);
        setConfig("WIDTH", 2);
        setConfig("OBSTACLE_MODE", "none");

        // Create world with clean maze
        world = new World(false);
        Maze emptyMaze = new Maze("none");

        // Inject empty maze and obstacle list
        setField(world, "maze", emptyMaze);
        setField(world, "obstacleList", emptyMaze.getObstacles());

        // Launch HAL at (0,0) facing NORTH
        hal = new Robot("HAL", "soldier");
        hal.setPosition(new Position(0, 0));
        hal.setCurrentDirection(NORTH);
        world.addRobot(hal);
        world.setCurrentRobot(hal);

        // Add mountain at (0,1)
        world.getObstacles().add(new Obstacle(0, 1, 0, 1, ObstacleType.MOUNTAIN));

        // Add 3 other robots at (0,1) — same as the obstacle
        List<String> robotNames = List.of("R2D2", "BOSS", "OPTIMUS", "WALL-E", "MEGA", "EVA", "SAM");
        for (int i = 0; i < 7; i++) {
            Robot bot = new Robot(robotNames.get(i), "soldier");
            bot.setPosition(new Position(0, 1));  // same position
            world.addRobot(bot);
        }
    }

    @Test
    @DisplayName("AT-11.1.2: Look detects 1 obstacle and 3 robots at distance 1")
    void testLookDetectsObstacleAndRobotsAtSameDistance() {
        JSONObject command = new JSONObject()
                .put("command", "look")
                .put("robot", "HAL");

        String response = world.handleCommand(command.toString());
        System.out.println("Look Response: " + response);

        JSONObject result = new JSONObject(response);
        assertEquals("OK", result.getString("result"), "Response should be OK");

        JSONArray objects = result.getJSONObject("data").getJSONArray("objects");

        // There may be more than one object at the same distance.
        // We will assert that one is an obstacle, and three are robots (first three found)
        int obstacleCount = 0;
        int robotCount = 0;

        for (int i = 0; i < objects.length(); i++) {
            JSONObject obj = objects.getJSONObject(i);
            assertEquals(1, obj.getInt("distance"), "All objects should be at distance 1");
            if (obj.getString("type").equals("mountain")) obstacleCount++;
            if (obj.getString("type").equals("robot")) robotCount++;
        }

        assertTrue(obstacleCount >= 1, "Should detect at least one obstacle");
        assertTrue(robotCount >= 3, "Should detect at least three robots");
    }

    // === Utilities ===

    private void setConfig(String fieldName, Object value) throws Exception {
        Field field = Config.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    private void setField(Object target, String fieldName, Object value) throws Exception {
        Field field = World.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }
}
