package acceptance_test.look;

import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Position;
import za.co.wethinkcode.robots.config.Config;
import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.obstacle.Obstacle;
import za.co.wethinkcode.robots.obstacle.ObstacleType;
import za.co.wethinkcode.robots.robot.Robot;
import za.co.wethinkcode.robots.world.World;

import java.lang.reflect.Field;

import static org.junit.jupiter.api.Assertions.*;
import static za.co.wethinkcode.robots.Direction.NORTH;

public class LookDetectObstacleTest {
    private World world;
    private Robot testRobot;

    @BeforeEach
    void setUp() throws Exception {
        // Set world size to 2x2
        setConfig("HEIGHT", 2);
        setConfig("WIDTH", 2);
        setConfig("OBSTACLE_MODE", "none");

        // Create world with empty maze
        world = new World(false);
        Maze emptyMaze = new Maze("none");

        // Inject empty maze into world
        setWorldField("maze", emptyMaze);
        setWorldField("obstacleList", emptyMaze.getObstacles());

        // Create robot HAL at (0,0) facing NORTH
        testRobot = new Robot("HAL", "soldier");
        testRobot.setPosition(new Position(0, 0));
        testRobot.setCurrentDirection(NORTH);
        world.addRobot(testRobot);
        world.setCurrentRobot(testRobot);

        // Place obstacle at (0,1)
        world.getObstacles().add(new Obstacle(0, 1, 0, 1, ObstacleType.MOUNTAIN));
    }

    @Test
    @DisplayName("AT-11.1.1: Look detects an obstacle 1 step away")
    void testLookDetectsObstacleOneStepAway() {
        JSONObject command = new JSONObject()
                .put("command", "look")
                .put("robot", "HAL");

        String response = world.handleCommand(command.toString());
        System.out.println("Look Response: " + response);

        JSONObject result = new JSONObject(response);
        assertEquals("OK", result.getString("result"), "Response should be OK");

        JSONArray objects = result.getJSONObject("data").getJSONArray("objects");
        assertEquals(1, objects.length(), "Should detect exactly one object");

        JSONObject obj = objects.getJSONObject(0);
        assertEquals("mountain", obj.getString("type"), "Should detect a mountain");
        assertEquals(1, obj.getInt("distance"), "Distance should be 1 step");
    }

    // Utility: set static Config field
    private void setConfig(String fieldName, Object value) throws Exception {
        Field field = Config.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    // Utility: inject field into World instance
    private void setWorldField(String fieldName, Object value) throws Exception {
        Field field = World.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(world, value);
    }
}
