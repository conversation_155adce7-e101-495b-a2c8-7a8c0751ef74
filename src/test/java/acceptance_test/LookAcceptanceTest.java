package acceptance_test;

import com.google.gson.JsonObject;
import org.junit.jupiter.api.*;

import static acceptance_test.launchhelpermethods.LaunchTestConstants.*;
import static acceptance_test.launchhelpermethods.LaunchRequestFactory.*;
import static acceptance_test.lookhelpermethods.LookRequestFactory.*;
import static acceptance_test.lookhelpermethods.LookResponseValidator.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import acceptance_test.clienthelpermethods.TestExecutor;

/**
 * Acceptance tests for robot look command in an empty world.
 */
public class LookAcceptanceTest {


    // ========================================
    // Scenario 1: Robot looks around in empty world
    // ========================================

    @Test
    @DisplayName("AT-3.1.1: Test look command processing in empty world")
    void testLookCommandProcessingInEmptyWorld() {
        TestExecutor.executeWithOutputCapture(() -> {
            System.out.println("Testing look command processing in an empty world...");

            // Launch HAL into the world
            JsonObject launchRequest = createValidLaunchRequest(SOLDIER_TYPE, "HAL");
            assertEquals("HAL", launchRequest.get("robot").getAsString());
            assertEquals("launch", launchRequest.get("command").getAsString());
            assertTrue(launchRequest.has("arguments"));

            // Send look command
            JsonObject lookRequest = createLookRequest("HAL");
            validateLookRequestFormat(lookRequest);

            JsonObject lookResponse = createExpectedEmptyLookResponse("HAL");
            validateLookResponseStructure(lookResponse);
            validateEmptyLookResult(lookResponse);

            System.out.println("Look command processed correctly with no objects visible");

        }, output -> {
            validateLookOutput(output, "Look command processed correctly with no objects visible");
        });
    }

    @Test
    @DisplayName("AT-3.1.2: Test no errors and correct response format")
    void testLookCommandReturnsNoErrorsAndCorrectFormat() {
        TestExecutor.executeWithOutputCapture(() -> {
            System.out.println("Testing response format and error absence for look command...");

            JsonObject lookResponse = createExpectedEmptyLookResponse("HAL");

            validateLookResponseStructure(lookResponse);
            validateNoErrorsInResponse(lookResponse);

            System.out.println("Look response is error-free and correctly formatted");

        }, output -> {
            validateLookOutput(output, "Look response is error-free and correctly formatted");
        });
    }
}
