package acceptance_test.AsciiWorld;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.world.AsciiWorld;
import za.co.wethinkcode.robots.world.World;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

public class AsciiFileOutputTest {
    @BeforeEach
    void setUp(){
        World testworld=new World(false);
        AsciiWorld textworld=new AsciiWorld(testworld);
        textworld.buildAscii();

    }

    @Test
    void FileExists() throws IOException {
        File file = new File("Ascii-World.txt");
        file.createNewFile();
        assertTrue(file.exists(), "File should exist after creation");
        file.delete();
    }

    @Test
    void fileHasLegend() throws IOException {
        File file = new File("Ascii-World.txt");
        String content = Files.readString(file.toPath());
//        String expected= "\"\\n\\n\" +\n                       \"\\n\" +\n                       \"LAKE :          '~'\\n\" +\n                       \"BOTTOMLESS_PIT :'■'\\n\"";
        assertTrue(content.contains("------Legend-------"));
        assertTrue(content.contains("MOUNTAIN :      '^'"));
        assertTrue(content.contains("LAKE :          '~'"));
        assertTrue(content.contains("BOTTOMLESS_PIT :'■'"));
    }
    @Test
    void gridGenerated() throws IOException {
        File file = new File("Ascii-World.txt");
        String content = Files.readString(file.toPath());
        String s = "\n";
        String expected= """
                ------Legend-------
                MOUNTAIN :      '^'
                LAKE :          '~'
                BOTTOMLESS_PIT :'■'
                 ____________________
                |                    |
                |      ~~~           |
                |     ~~~~~          |
                |     ~~~~~          |
                |     ~~~~~          |
                |      ~~~           |
                |                    |
                |                    |
                |                    |
                |  ■■                |
                |  ■■                |
                |                    |
                |                    |
                |                    |
                |                    |
                |    ^          ~~   |
                |   ^^^         ~~   |
                |  ^^^^^             |
                |  ^^^^^             |
                |                    |
                 --------------------""" + s;
        assertEquals(expected.trim(),content.trim().replace("\r\n","\n"));

    }
}
