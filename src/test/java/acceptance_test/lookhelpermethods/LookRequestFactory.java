package acceptance_test.lookhelpermethods;

import com.google.gson.JsonObject;

public class LookRequestFactory {

    public static JsonObject createLookRequest(String robotName) {
        JsonObject request = new JsonObject();
        request.addProperty("robot", robotName);
        request.addProperty("command", "look");
        return request;
    }

    public static void validateLookRequestFormat(JsonObject request) {
        assert request.has("robot") : "'robot' field missing";
        assert request.has("command") : "'command' field missing";
        assert request.get("command").getAsString().equals("look") : "'command' should be 'look'";
    }
}
