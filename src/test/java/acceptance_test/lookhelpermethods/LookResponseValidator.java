package acceptance_test.lookhelpermethods;

import com.google.gson.JsonObject;
import com.google.gson.JsonArray;

import static org.junit.jupiter.api.Assertions.*;

public class LookResponseValidator {

    public static void validateLookResponseStructure(JsonObject response) {
        assertTrue(response.has("result"), "'result' field missing");
        assertEquals("OK", response.get("result").getAsString(), "Expected result to be 'OK'");

        assertTrue(response.has("data"), "'data' field missing");
        assertTrue(response.get("data").isJsonObject(), "'data' should be a JSON object");

        JsonObject data = response.getAsJsonObject("data");
        assertTrue(data.has("objects"), "'objects' missing in data");
        assertTrue(data.get("objects").isJsonArray(), "'objects' should be a JSON array");
    }

    public static void validateEmptyLookResult(JsonObject response) {
        JsonArray objects = response.getAsJsonObject("data").getAsJsonArray("objects");
        assertEquals(0, objects.size(), "Expected no visible objects");
    }

    public static void validateNoErrorsInResponse(JsonObject response) {
        assertFalse(response.has("error"), "No error should be present in look response");
    }

    public static void validateLookOutput(String output, String expectedMessage) {
        assertTrue(output.contains(expectedMessage), "Expected output to contain: " + expectedMessage);
    }

    public static JsonObject createExpectedEmptyLookResponse(String robotName) {
        JsonObject response = new JsonObject();
        response.addProperty("result", "OK");

        JsonObject data = new JsonObject();
        data.add("objects", new JsonArray());

        response.add("data", data);
        return response;
    }
}
