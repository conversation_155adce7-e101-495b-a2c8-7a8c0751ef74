package acceptance_test.launchhelpermethods;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import static acceptance_test.launchhelpermethods.LaunchTestConstants.*;

public class LaunchRequestFactory {

    public static JsonObject createValidLaunchRequest(String robotType, String robotName) {
        JsonArray arguments = new JsonArray();
        arguments.add(robotType);
        return buildLaunchRequest(robotName, arguments);
    }

    public static JsonObject createValidLaunchRequestProtocol(String robotType, String robotName, int maxShields, int maxShots) {
        JsonArray arguments = new JsonArray();
        arguments.add(robotType);
        arguments.add(maxShields);
        arguments.add(maxShots);
        return buildLaunchRequest(robotName, arguments);
    }

    private static JsonObject buildLaunchRequest(String robotName, JsonArray arguments) {
        JsonObject request = new JsonObject();
        request.addProperty(ROBOT_FIELD, robotName);
        request.addProperty(COMMAND_FIELD, LAUNCH_COMMAND);
        request.add(ARGUMENTS_FIELD, arguments);
        return request;
    }
}
