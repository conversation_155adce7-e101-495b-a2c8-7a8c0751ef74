package acceptance_test.launchhelpermethods;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.io.*;
import java.net.Socket;
import static acceptance_test.clienthelpermethods.TestConstants.*;

public class ServerCommunicator {

    public static JsonObject sendLaunchRequest(JsonObject request) throws Exception {
        Socket socket = new Socket(TEST_HOST, TEST_PORT);
        PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
        BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));

        out.println(request.toString());
        String response = in.readLine();

        socket.close();
        return JsonParser.parseString(response).getAsJsonObject();
    }
}
