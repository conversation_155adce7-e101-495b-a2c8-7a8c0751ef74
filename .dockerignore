# Compiled class files
*.class

# Git
.git
# Package Files
*.jar
!target/robot-world-*-jar-with-dependencies.jar
*.war
*.ear

# Log Files
*.log

# Build directories
target/
!target/robot-world-*-jar-with-dependencies.jar
build/
out/

# IDE specific files
.idea/
*.iml
.classpath
.project
.settings/
nbproject/
.vscode/

# OS generated files
.DS_Store
Thumbs.db

# Documentation
docs/
*.md

# Test Reports
surefire-reports/

# Docker
Dockerfile
.dockerignore

# GitLab CI/CD
.gitlab-ci.yml

# Build scripts
build.bat
Makefile

# Reference server
libs/reference-server-*.jar

# Temporary files
*.tmp
*~
*.temp

# Backup files
*.bak
*.backup